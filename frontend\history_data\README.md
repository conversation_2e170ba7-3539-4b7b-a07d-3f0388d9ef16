# 历史方案对比功能

本目录用于存储仿真方案的历史数据，支持前端的方案对比功能。

## 文件结构

- `index.json`: 索引文件，包含所有历史方案的元数据
- `simulation_*.json`: 各个历史方案的详细数据文件

## index.json 格式

```json
[
  {
    "id": "250702174456",
    "name": "方案A - 东侧出入口",
    "date": "2025-07-02 17:44:56",
    "file_path": "history_data/simulation_250702174456.json",
    "description": "测试东侧出入口开放方案"
  },
  ...
]
```

## 方案数据文件格式

```json
{
  "simulation_id": "250702174456",
  "name": "方案A - 东侧出入口",
  "description": "测试东侧出入口开放方案",
  "start_time": "2025-07-02T17:44:56.000Z",
  "config_summary": {
    "network": "预设路网 - 仅开放东侧出入口 + 道路限行(2个路段)",
    "signal": "预设配时 + 自定义优化(2个交叉口)",
    "traffic": "预设需求 - 进场场景 + 贵宾专车"
  },
  "simulation_results": {
    "network_metrics": {
      // 各种指标数据
    },
    "selected_edge_metrics": {
      // 可选：用户自选路段指标数据
    }
  }
}
```

## API接口

### 保存方案

- URL: `/api/save-scheme`
- 方法: `POST`
- 请求体:
  ```json
  {
    "id": "scheme_1234567890",
    "name": "方案名称",
    "description": "方案描述",
    "date": "2025-07-10T10:20:52.000Z",
    "file_path": "history_data/scheme_1234567890.json",
    "data": { /* 方案完整数据 */ }
  }
  ```

### 删除方案

- URL: `/api/delete-scheme`
- 方法: `POST`
- 请求体:
  ```json
  {
    "id": "scheme_1234567890",
    "file_path": "history_data/scheme_1234567890.json"
  }
  ```

## 使用方法

1. 在仿真页面中点击"历史方案"按钮打开历史方案列表
2. 点击"对比"按钮选择要与当前方案进行对比的历史方案
3. 在对比模式下，可以看到两个方案的指标数据并排显示
4. 点击"恢复原始数据"按钮或对比窗口的关闭按钮可退出对比模式 