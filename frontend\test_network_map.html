<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>路网地图加载测试</title>
    <link rel="stylesheet" href="https://unpkg.com/leaflet@1.9.4/dist/leaflet.css" />
    <style>
        body {
            margin: 0;
            padding: 20px;
            background-color: #05202e;
            color: #95D7E3;
            font-family: Arial, sans-serif;
        }
        
        #map-container {
            width: 100%;
            height: 600px;
            border: 1px solid #30abe8;
            border-radius: 8px;
            overflow: hidden;
            background-color: #05202e;
        }
        
        .map-placeholder {
            width: 100%;
            height: 100%;
            display: flex;
            flex-direction: column;
            justify-content: center;
            align-items: center;
            background-color: #05202e;
        }
        
        .leaflet-control-zoom a {
            background-color: rgba(16, 39, 53, 0.9) !important;
            color: #95D7E3 !important;
            border-color: #30abe8 !important;
        }
        
        .leaflet-control-zoom a:hover {
            background-color: rgba(48, 171, 232, 0.3) !important;
            color: #47ebeb !important;
        }
        
        .status {
            margin-bottom: 20px;
            padding: 10px;
            background-color: #102735;
            border-radius: 4px;
        }
        
        .controls {
            margin-bottom: 20px;
        }
        
        button {
            background-color: #30abe8;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 4px;
            cursor: pointer;
            margin-right: 10px;
        }
        
        button:hover {
            background-color: #47ebeb;
        }
    </style>
</head>
<body>
    <h1>路网地图加载测试</h1>
    <div class="status" id="status">准备加载地图...</div>
    <div class="controls">
        <button onclick="initializeNetworkMap()">初始化地图</button>
        <button onclick="loadNetworkBackgroundImage()">加载底图</button>
        <button onclick="loadNetworkData()">加载路网</button>
    </div>
    <div id="map-container">
        <div class="map-placeholder">
            <i class="bi bi-map" style="font-size: 48px;"></i>
            <p>点击"初始化地图"开始</p>
        </div>
    </div>

    <script src="https://unpkg.com/leaflet@1.9.4/dist/leaflet.js"></script>
    <script>
        let networkMap = null;
        let networkBackgroundImageLayer = null;
        
        // 地图配置参数
        const networkMapConfig = {
            minZoom: -5,
            maxZoom: 6,
            defaultZoom: -1,
            defaultCenter: [3000, 4700]
        };
        
        // 颜色配置
        const networkColors = {
            road: '#4a4a4a',
            roadBorder: '#333333',
            junction: '#666666'
        };
        
        function updateNetworkMapStatus(message) {
            document.getElementById('status').textContent = message;
            console.log('路网地图状态:', message);
        }
        
        // 加载底图
        function loadNetworkBackgroundImage() {
            if (!networkMap) {
                updateNetworkMapStatus('请先初始化地图');
                return;
            }
            
            updateNetworkMapStatus('正在加载底图...');
            
            fetch('http://localhost:8888/api/background-image')
                .then(response => response.json())
                .then(data => {
                    if (data.image) {
                        // 移除现有的底图（如果有）
                        if (networkBackgroundImageLayer) {
                            networkBackgroundImageLayer.remove();
                        }
                        
                        // 计算底图边界
                        const centerX = data.centerX || 0;
                        const centerY = data.centerY || 0;
                        const width = data.width || 1000;
                        const height = data.height || 1000;
                        
                        // 计算图像边界
                        const halfWidth = width / 2;
                        const halfHeight = height / 2;
                        
                        // 计算图像四个角的坐标
                        const southWest = networkMap.unproject([centerX - halfWidth, -(centerY + halfHeight)], 0);
                        const northEast = networkMap.unproject([centerX + halfWidth, -(centerY - halfHeight)], 0);
                        const bounds = L.latLngBounds(southWest, northEast);
                        
                        // 创建图像覆盖层
                        const imageUrl = `http://localhost:8888/files/${data.image}`;
                        networkBackgroundImageLayer = L.imageOverlay(imageUrl, bounds, {
                            opacity: 0.8,
                            interactive: false
                        }).addTo(networkMap);
                        
                        updateNetworkMapStatus('底图加载完成');
                        networkMap.setView(networkMapConfig.defaultCenter, networkMapConfig.defaultZoom);
                    }
                })
                .catch(error => {
                    console.error('加载底图失败:', error);
                    updateNetworkMapStatus('底图加载失败: ' + error.message);
                });
        }
        
        // 加载路网数据
        function loadNetworkData() {
            if (!networkMap) {
                updateNetworkMapStatus('请先初始化地图');
                return;
            }
            
            updateNetworkMapStatus('正在加载路网...');
            
            fetch('http://localhost:8888/api/network')
                .then(response => response.json())
                .then(data => {
                    if (data.features && data.features.length > 0) {
                        // 分离不同类型的要素
                        const lanePolygons = data.features.filter(f => f.properties.type === 'lane_polygon');
                        const junctions = data.features.filter(f => f.properties.type === 'junction');
                        
                        // 先添加车道多边形
                        if (lanePolygons.length > 0) {
                            L.geoJSON({
                                type: "FeatureCollection",
                                features: lanePolygons
                            }, {
                                coordsToLatLng: function(coords) {
                                    return networkMap.unproject([coords[0], -coords[1]], 0);
                                },
                                style: function(feature) {
                                    return {
                                        color: networkColors.roadBorder,
                                        weight: 1,
                                        opacity: 1,
                                        fillColor: networkColors.road,
                                        fillOpacity: 0.8
                                    };
                                }
                            }).addTo(networkMap);
                        }
                        
                        // 然后添加交叉口
                        if (junctions.length > 0) {
                            L.geoJSON({
                                type: "FeatureCollection",
                                features: junctions
                            }, {
                                coordsToLatLng: function(coords) {
                                    return networkMap.unproject([coords[0], -coords[1]], 0);
                                },
                                style: function(feature) {
                                    return {
                                        color: "#000000",
                                        weight: 1,
                                        opacity: 1,
                                        fillColor: networkColors.junction,
                                        fillOpacity: 0.6
                                    };
                                }
                            }).addTo(networkMap);
                        }
                        
                        updateNetworkMapStatus(`路网加载完成！显示了 ${lanePolygons.length} 个车道和 ${junctions.length} 个交叉口`);
                    } else {
                        updateNetworkMapStatus('路网数据为空');
                    }
                })
                .catch(error => {
                    console.error('加载路网失败:', error);
                    updateNetworkMapStatus('路网加载失败: ' + error.message);
                });
        }
        
        // 初始化路网地图
        function initializeNetworkMap() {
            updateNetworkMapStatus('正在初始化Leaflet地图...');
            
            // 获取地图容器
            const mapContainer = document.getElementById('map-container');
            if (!mapContainer) {
                console.error('找不到地图容器');
                return;
            }
            
            // 清空容器内容并创建地图div
            mapContainer.innerHTML = '';
            const mapDiv = document.createElement('div');
            mapDiv.id = 'network-map';
            mapDiv.style.width = '100%';
            mapDiv.style.height = '100%';
            mapDiv.style.minHeight = '600px';
            mapContainer.appendChild(mapDiv);
            
            // 初始化Leaflet地图
            networkMap = L.map('network-map', {
                crs: L.CRS.Simple,
                minZoom: networkMapConfig.minZoom,
                maxZoom: networkMapConfig.maxZoom,
                zoomControl: true
            });
            
            // 设置初始视图
            networkMap.setView(networkMapConfig.defaultCenter, networkMapConfig.defaultZoom);
            
            updateNetworkMapStatus('地图初始化完成，可以加载底图和路网了');
        }
        
        // 自动初始化并加载完整地图
        function autoLoadFullMap() {
            initializeNetworkMap();
            setTimeout(() => {
                loadNetworkBackgroundImage();
                setTimeout(() => {
                    loadNetworkData();
                }, 1000);
            }, 500);
        }
        
        // 页面加载完成后自动加载地图
        document.addEventListener('DOMContentLoaded', function() {
            setTimeout(autoLoadFullMap, 1000);
        });
    </script>
</body>
</html>
