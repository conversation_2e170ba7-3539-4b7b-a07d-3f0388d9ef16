import xml.etree.ElementTree as ET
from collections import defaultdict
import os

class MetricsCalculator:
    def __init__(self, sim_id):
        """
        初始化指标计算器
        
        参数:
        sim_id: str - 仿真ID，用于定位输出文件
        """
        self.base_dir = 'sumo_data'
        self.sim_dir = os.path.join(self.base_dir, sim_id)
        
    def calculate_metrics(self, config):
        """
        根据仿真类型计算相应指标
        
        参数:
        config: dict - JSON配置
        """
        metrics = {}
        
        # 基础指标：行人和车辆
        metrics.update(self._calculate_pedestrian_metrics())
        metrics.update(self._calculate_vehicle_metrics())
        
        # 根据配置决定计算哪些额外指标
        traffic_config = config['traffic_config']
        
        # 检查是否为VIP场景
        if (traffic_config.get('type') == 'predefined' and 
            traffic_config.get('vehicle_type') == '存在贵宾专车'):
            metrics.update(self._calculate_vip_metrics())
        
        # 检查是否为离场场景
        if (traffic_config.get('type') == 'predefined' and 
            traffic_config.get('scenario') == '离场'):
            metrics.update(self._calculate_venue_metrics())
        
        # 检查是否启用了路段分析
        analysis_config = config.get('analysis_config', {})
        edge_analysis = analysis_config.get('edge_analysis', {})
        if edge_analysis.get('enabled', False):
            selected_edges = edge_analysis.get('selected_edges', [])
            if selected_edges:
                print(f"计算用户选择的 {len(selected_edges)} 个路段的指标...")
                edge_metrics = self.calculate_selected_edge_metrics(selected_edges)
                metrics.update(edge_metrics)
            
        return metrics
    
    def calculate_selected_edge_metrics(self, edge_ids):
        """
        计算用户自选路段的聚合指标
        
        参数:
        edge_ids: list - 用户选择的路段ID列表
        
        返回:
        dict - 包含聚合指标的字典
        """
        if not edge_ids:
            return {'selected_edge_metrics': {}}
        
        print(f"计算自选路段聚合指标，路段数量: {len(edge_ids)}")
        print(f"路段ID列表: {edge_ids}")
        
        # 计算所有选择路段的聚合行人指标
        pedestrian_metrics = self._calculate_aggregated_pedestrian_metrics(edge_ids)
        
        # 计算所有选择路段的聚合车辆指标
        vehicle_metrics = self._calculate_aggregated_vehicle_metrics(edge_ids)
        
        return {
            'selected_edge_metrics': {
                'pedestrian_metrics': pedestrian_metrics,
                'vehicle_metrics': vehicle_metrics
            }
        }
    
    def _calculate_pedestrian_metrics(self):
        """计算行人相关指标"""
        metrics = {}
        
        # 读取tripinfo文件
        tripinfo_file = os.path.join(self.sim_dir, 'output.tripinfo.xml')
        if os.path.exists(tripinfo_file):
            tree = ET.parse(tripinfo_file)
            root = tree.getroot()
            
            # 统计数据
            total_duration = 0
            total_waitingTime = 0
            total_waitingCount = 0
            total_timeLoss = 0
            count = 0
            
            # 从personinfo中获取行人数据
            for personinfo in root.findall('personinfo'):
                if personinfo.get('id').startswith('gym_people'):
                    total_duration += float(personinfo.get('duration', 0))
                    total_waitingTime += float(personinfo.get('waitingTime', 0))
                    total_waitingCount += int(personinfo.get('waitingCount', 0))
                    total_timeLoss += float(personinfo.get('timeLoss', 0))
                    count += 1
            
            # 从tripinfo中获取行人数据
            for tripinfo in root.findall('tripinfo'):
                if tripinfo.get('id').startswith('gym_people'):
                    total_duration += float(tripinfo.get('duration', 0))
                    total_waitingTime += float(tripinfo.get('waitingTime', 0))
                    total_waitingCount += int(tripinfo.get('waitingCount', 0))
                    total_timeLoss += float(tripinfo.get('timeLoss', 0))
                    count += 1
            
            if count > 0:
                metrics['avg_duration'] = total_duration / count
                metrics['avg_waitingTime'] = total_waitingTime / count
                metrics['avg_waitingCount'] = total_waitingCount / count
                metrics['avg_timeLoss'] = total_timeLoss / count
            else:
                print("No pedestrian entries were found.")
                metrics = {
                    'avg_duration': 0,
                    'avg_waitingTime': 0,
                    'avg_waitingCount': 0,
                    'avg_timeLoss': 0
                }
        
        return {'pedestrian_metrics': metrics}
    
    def _calculate_vehicle_metrics(self):
        """计算车辆相关指标"""
        metrics = {}
        
        tripinfo_file = os.path.join(self.sim_dir, 'output.tripinfo.xml')
        if os.path.exists(tripinfo_file):
            tree = ET.parse(tripinfo_file)
            root = tree.getroot()
            
            # 统计一般车辆的信息
            vehicle_data = defaultdict(list)
            for tripinfo in root.findall('tripinfo'):
                # 使用统一的前缀，排除VIP车辆
                if tripinfo.get('id').startswith('gym_vehicle') and not tripinfo.get('id').startswith('gym_vip'):
                    vehicle_data['duration'].append(float(tripinfo.get('duration')))
                    vehicle_data['waiting_time'].append(float(tripinfo.get('waitingTime')))
                    vehicle_data['waiting_count'].append(float(tripinfo.get('waitingCount', 0)))
                    vehicle_data['time_loss'].append(float(tripinfo.get('timeLoss')))
            
            # 计算平均值
            if vehicle_data['duration']:  # 如果有数据
                metrics['avg_duration'] = sum(vehicle_data['duration']) / len(vehicle_data['duration'])
                metrics['avg_waiting_time'] = sum(vehicle_data['waiting_time']) / len(vehicle_data['waiting_time'])
                metrics['avg_waiting_count'] = sum(vehicle_data['waiting_count']) / len(vehicle_data['waiting_count'])
                metrics['avg_time_loss'] = sum(vehicle_data['time_loss']) / len(vehicle_data['time_loss'])
            else:
                metrics['avg_duration'] = 0
                metrics['avg_waiting_time'] = 0
                metrics['avg_waiting_count'] = 0
                metrics['avg_time_loss'] = 0
        
        return {'vehicle_metrics': metrics}
    
    def _calculate_vip_metrics(self):
        """计算贵宾专车相关指标"""
        metrics = {}
        
        tripinfo_file = os.path.join(self.sim_dir, 'output.tripinfo.xml')
        if os.path.exists(tripinfo_file):
            tree = ET.parse(tripinfo_file)
            root = tree.getroot()
            
            # 统计VIP车辆的信息
            vip_data = defaultdict(list)
            for tripinfo in root.findall('tripinfo'):
                if tripinfo.get('id').startswith('vip'):
                    vip_data['duration'].append(float(tripinfo.get('duration')))
                    vip_data['waiting_time'].append(float(tripinfo.get('waitingTime')))
                    vip_data['waiting_count'].append(float(tripinfo.get('waitingCount', 0)))
                    vip_data['time_loss'].append(float(tripinfo.get('timeLoss')))
            
            # 计算平均值
            if vip_data['duration']:  # 如果有数据
                metrics['vip_avg_duration'] = sum(vip_data['duration']) / len(vip_data['duration'])
                metrics['vip_avg_waiting_time'] = sum(vip_data['waiting_time']) / len(vip_data['waiting_time'])
                metrics['vip_avg_waiting_count'] = sum(vip_data['waiting_count']) / len(vip_data['waiting_count'])
                metrics['vip_avg_time_loss'] = sum(vip_data['time_loss']) / len(vip_data['time_loss'])
            else:
                metrics['vip_avg_duration'] = 0
                metrics['vip_avg_waiting_time'] = 0
                metrics['vip_avg_waiting_count'] = 0
                metrics['vip_avg_time_loss'] = 0
        
        return {'vip_metrics': metrics}
    
    def _calculate_venue_metrics(self):
        """计算场馆周围指标（离场场景）"""
        # 解析行人的edgedata XML文件
        people_edgedata_path = os.path.join(self.sim_dir, 'output.people_edgedata.xml')
        try:
            people_edge_tree = ET.parse(people_edgedata_path)
            people_edge_root = people_edge_tree.getroot()
        except ET.ParseError as e:
            print(f"Error parsing the people edgedata XML file: {e}")
            return None
        except FileNotFoundError:
            print("The people edgedata file was not found.")
            return None

        # 计算行人的统计数据
        people_total_traveltime = 0
        people_total_timeloss = 0
        people_total_entered = 0

        # 遍历所有edge计算行人数据
        for interval in people_edge_root.findall('interval'):
            for edge in interval.findall('edge'):
                entered = float(edge.get('entered', 0))
                if entered > 0:
                    traveltime = float(edge.get('traveltime', 0))
                    timeloss = float(edge.get('timeLoss', 0))
                    
                    people_total_traveltime += traveltime * entered
                    people_total_timeloss += timeloss 
                    people_total_entered += entered

        # 计算行人平均值
        people_avg_traveltime = people_total_traveltime / people_total_entered if people_total_entered > 0 else 0
        people_avg_timeloss = people_total_timeloss / people_total_entered if people_total_entered > 0 else 0

        print(f"people_total_entered: {people_total_entered}, people_avg_traveltime: {people_avg_traveltime}, people_avg_timeloss: {people_avg_timeloss}")

        # 解析车辆的edgedata XML文件
        vehicle_edgedata_path = os.path.join(self.sim_dir, 'output.vehicle_edgedata.xml')
        try:
            vehicle_edge_tree = ET.parse(vehicle_edgedata_path)
            vehicle_edge_root = vehicle_edge_tree.getroot()
        except ET.ParseError as e:
            print(f"Error parsing the vehicle edgedata XML file: {e}")
            return None
        except FileNotFoundError:
            print("The vehicle edgedata file was not found.")
            return None

        # 计算车辆的统计数据
        vehicle_total_traveltime = 0
        vehicle_total_timeloss = 0
        vehicle_total_entered = 0

        # 遍历所有edge计算车辆数据
        for interval in vehicle_edge_root.findall('interval'):
            for edge in interval.findall('edge'):
                entered = float(edge.get('entered', 0))
                if entered > 0:
                    traveltime = float(edge.get('traveltime', 0))
                    timeloss = float(edge.get('timeLoss', 0))
                    
                    vehicle_total_traveltime += traveltime * entered
                    vehicle_total_timeloss += timeloss 
                    vehicle_total_entered += entered

        # 计算车辆平均值
        vehicle_avg_traveltime = vehicle_total_traveltime / vehicle_total_entered if vehicle_total_entered > 0 else 0
        vehicle_avg_timeloss = vehicle_total_timeloss / vehicle_total_entered if vehicle_total_entered > 0 else 0
        
        return {'venue_metrics': {
            'people_avg_traveltime': people_avg_traveltime,
            'people_avg_timeloss': people_avg_timeloss,
            'vehicle_avg_traveltime': vehicle_avg_traveltime,
            'vehicle_avg_timeloss': vehicle_avg_timeloss
        }} 

    def _calculate_aggregated_pedestrian_metrics(self, edge_ids):
        """计算所有选择路段的聚合行人指标"""
        # 读取行人edgedata文件 - 优先使用用户选择路段的数据文件
        people_edgedata_path = os.path.join(self.sim_dir, 'output.selected_people_edgedata.xml')
        if not os.path.exists(people_edgedata_path):
            # 如果没有用户选择路段的数据文件，使用默认的数据文件
            people_edgedata_path = os.path.join(self.sim_dir, 'output.people_edgedata.xml')
        
        if not os.path.exists(people_edgedata_path):
            print(f"警告: 行人edgedata文件不存在: {people_edgedata_path}")
            return {
                'total_departed': 0,
                'total_arrived': 0,
                'total_entered': 0,
                'avg_traveltime': 0,
                'avg_waitingTime': 0,
                'avg_speed': 0,
                'avg_timeLoss': 0,
                'avg_occupancy': 0
            }
        
        try:
            tree = ET.parse(people_edgedata_path)
            root = tree.getroot()
            
            # 聚合所有选择路段在所有时间间隔的数据
            total_departed = 0
            total_arrived = 0
            total_entered = 0
            weighted_traveltime = 0
            weighted_waitingTime = 0
            weighted_timeLoss = 0
            speed_values = []
            occupancy_values = []
            
            for interval in root.findall('interval'):
                for edge in interval.findall('edge'):
                    edge_id = edge.get('id')
                    if edge_id in edge_ids:
                        departed = float(edge.get('departed', 0))
                        arrived = float(edge.get('arrived', 0))
                        entered = float(edge.get('entered', 0))
                        traveltime = float(edge.get('traveltime', 0))
                        waitingTime = float(edge.get('waitingTime', 0))
                        speed = float(edge.get('speed', 0))
                        timeLoss = float(edge.get('timeLoss', 0))
                        occupancy = float(edge.get('occupancy', 0))
                        
                        # 累加总量
                        total_departed += departed
                        total_arrived += arrived
                        total_entered += entered
                        
                        # 累加加权值
                        if entered > 0:
                            weighted_traveltime += traveltime * entered
                            weighted_waitingTime += waitingTime 
                            weighted_timeLoss += timeLoss 
                        
                        # 收集速度和占用率数据
                        speed_values.append(speed)
                        occupancy_values.append(occupancy)
            
            # 计算平均值
            avg_traveltime = weighted_traveltime / total_entered if total_entered > 0 else 0
            avg_waitingTime = weighted_waitingTime / total_entered if total_entered > 0 else 0
            avg_timeLoss = weighted_timeLoss / total_entered if total_entered > 0 else 0
            avg_speed = sum(speed_values) / len(speed_values) if speed_values else 0
            avg_occupancy = sum(occupancy_values) / (100.0 * len(occupancy_values)) if occupancy_values else 0
            
            return {
                'total_departed': total_departed,
                'total_arrived': total_arrived,
                'total_entered': total_entered,
                'avg_traveltime': avg_traveltime,
                'avg_waitingTime': avg_waitingTime,
                'avg_speed': avg_speed,
                'avg_timeLoss': avg_timeLoss,
                'avg_occupancy': avg_occupancy
            }
            
        except ET.ParseError as e:
            print(f"解析行人edgedata文件出错: {e}")
        except Exception as e:
            print(f"计算聚合行人路段指标出错: {e}")
        
        return {
            'total_departed': 0,
            'total_arrived': 0,
            'total_entered': 0,
            'avg_traveltime': 0,
            'avg_waitingTime': 0,
            'avg_speed': 0,
            'avg_timeLoss': 0,
            'avg_occupancy': 0
        }

    def _calculate_aggregated_vehicle_metrics(self, edge_ids):
        """计算所有选择路段的聚合车辆指标"""
        # 读取车辆edgedata文件 - 优先使用用户选择路段的数据文件
        vehicle_edgedata_path = os.path.join(self.sim_dir, 'output.selected_vehicle_edgedata.xml')
        if not os.path.exists(vehicle_edgedata_path):
            # 如果没有用户选择路段的数据文件，使用默认的数据文件
            vehicle_edgedata_path = os.path.join(self.sim_dir, 'output.vehicle_edgedata.xml')
        
        if not os.path.exists(vehicle_edgedata_path):
            print(f"警告: 车辆edgedata文件不存在: {vehicle_edgedata_path}")
            return {
                'total_departed': 0,
                'total_arrived': 0,
                'total_entered': 0,
                'avg_traveltime': 0,
                'avg_waitingTime': 0,
                'avg_speed': 0,
                'avg_timeLoss': 0,
                'avg_occupancy': 0
            }
        
        try:
            tree = ET.parse(vehicle_edgedata_path)
            root = tree.getroot()
            
            # 聚合所有选择路段在所有时间间隔的数据
            total_departed = 0
            total_arrived = 0
            total_entered = 0
            weighted_traveltime = 0
            weighted_waitingTime = 0
            weighted_timeLoss = 0
            speed_values = []
            occupancy_values = []
            
            for interval in root.findall('interval'):
                for edge in interval.findall('edge'):
                    edge_id = edge.get('id')
                    if edge_id in edge_ids:
                        departed = float(edge.get('departed', 0))
                        arrived = float(edge.get('arrived', 0))
                        entered = float(edge.get('entered', 0))
                        traveltime = float(edge.get('traveltime', 0))
                        waitingTime = float(edge.get('waitingTime', 0))
                        speed = float(edge.get('speed', 0))
                        timeLoss = float(edge.get('timeLoss', 0))
                        occupancy = float(edge.get('occupancy', 0))
                        
                        # 累加总量
                        total_entered += entered
                        total_arrived += arrived
                        total_entered += entered
                        
                        # 累加加权值
                        if entered > 0:
                            weighted_traveltime += traveltime * entered
                            weighted_waitingTime += waitingTime 
                            weighted_timeLoss += timeLoss 
                        
                        # 收集速度和占用率数据
                        speed_values.append(speed)
                        occupancy_values.append(occupancy)
            
            # 计算平均值
            avg_traveltime = weighted_traveltime / total_entered if total_entered > 0 else 0
            avg_waitingTime = weighted_waitingTime / total_entered if total_entered > 0 else 0
            avg_timeLoss = weighted_timeLoss / total_entered if total_entered > 0 else 0
            avg_speed = sum(speed_values) / len(speed_values) if speed_values else 0
            avg_occupancy = sum(occupancy_values) / (100.0 * len(occupancy_values)) if occupancy_values else 0
            
            return {
                'total_departed': total_departed,
                'total_arrived': total_arrived,
                'total_entered': total_entered,
                'avg_traveltime': avg_traveltime,
                'avg_waitingTime': avg_waitingTime,
                'avg_speed': avg_speed,
                'avg_timeLoss': avg_timeLoss,
                'avg_occupancy': avg_occupancy
            }
            
        except ET.ParseError as e:
            print(f"解析车辆edgedata文件出错: {e}")
        except Exception as e:
            print(f"计算聚合车辆路段指标出错: {e}")
        
        return {
            'total_departed': 0,
            'total_arrived': 0,
            'total_entered': 0,
            'avg_traveltime': 0,
            'avg_waitingTime': 0,
            'avg_speed': 0,
            'avg_timeLoss': 0,
            'avg_occupancy': 0
        }

 