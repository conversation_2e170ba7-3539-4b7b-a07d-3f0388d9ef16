/* 前端路网选择器样式 */

.network-selector-overlay {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background-color: rgba(0, 0, 0, 0.8);
    z-index: 9999;
    display: flex;
    justify-content: center;
    align-items: center;
}

.network-selector-modal {
    width: 95%;
    height: 90%;
    max-width: 1800px;
    background-color: #102735;
    border-radius: 8px;
    display: flex;
    flex-direction: column;
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.5);
}

.selector-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 15px 20px;
    border-bottom: 1px solid rgba(255, 255, 255, 0.1);
    background-color: #05202e;
    border-radius: 8px 8px 0 0;
}

.selector-header h3 {
    margin: 0;
    color: #ffffff;
    font-size: 18px;
}

.close-btn {
    background: none;
    border: none;
    color: #ffffff;
    font-size: 24px;
    cursor: pointer;
    padding: 0;
    width: 30px;
    height: 30px;
    display: flex;
    align-items: center;
    justify-content: center;
    border-radius: 4px;
    transition: background-color 0.2s;
}

.close-btn:hover {
    background-color: rgba(255, 255, 255, 0.1);
}

.selector-controls {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 15px 20px;
    border-bottom: 1px solid rgba(255, 255, 255, 0.1);
    background-color: #102735;
}

.selector-controls .info {
    display: flex;
    gap: 20px;
    color: #ffffff;
    font-size: 14px;
}

.selector-controls .buttons {
    display: flex;
    gap: 10px;
}

.selector-controls button {
    padding: 8px 16px;
    border: none;
    border-radius: 4px;
    cursor: pointer;
    font-size: 14px;
    transition: background-color 0.2s;
}

#selector-select-all {
    background-color: #4CAF50;
    color: white;
}

#selector-select-all:hover {
    background-color: #45a049;
}

#selector-clear-all {
    background-color: #f44336;
    color: white;
}

#selector-clear-all:hover {
    background-color: #da190b;
}

#selector-confirm {
    background-color: #2196F3;
    color: white;
}

#selector-confirm:hover:not(:disabled) {
    background-color: #0b7dda;
}

#selector-confirm:disabled {
    background-color: #666666;
    cursor: not-allowed;
}

#selector-toggle-names {
    background-color: #FF9800;
    color: white;
}

#selector-toggle-names:hover {
    background-color: #F57C00;
}

.selector-main {
    flex: 1;
    display: flex;
    min-height: 0;
}

.map-container {
    flex: 4;
    position: relative;
    background-color: #05202e;
}

#network-selector-canvas {
    width: 100%;
    height: 100%;
    cursor: move;
}

.zoom-controls {
    position: absolute;
    top: 10px;
    right: 10px;
    display: flex;
    flex-direction: column;
    gap: 5px;
}

.zoom-btn {
    width: 40px;
    height: 40px;
    border: none;
    border-radius: 4px;
    background-color: rgba(255, 255, 255, 0.1);
    color: #ffffff;
    font-size: 18px;
    font-weight: bold;
    cursor: pointer;
    transition: background-color 0.2s;
    display: flex;
    align-items: center;
    justify-content: center;
}

.zoom-btn:hover {
    background-color: rgba(255, 255, 255, 0.2);
}

.selector-sidebar {
    flex: 1;
    min-width: 300px;
    background-color: #102735;
    padding: 20px;
    border-left: 1px solid rgba(255, 255, 255, 0.1);
    overflow-y: auto;
}

.selector-sidebar h4 {
    margin: 0 0 15px 0;
    color: #ffffff;
    font-size: 16px;
}

.selector-sidebar h5 {
    margin: 20px 0 10px 0;
    color: #ffffff;
    font-size: 14px;
}

.selector-sidebar h6 {
    margin: 15px 0 8px 0;
    color: #cccccc;
    font-size: 13px;
    font-weight: bold;
}

.selected-list {
    background-color: rgba(255, 255, 255, 0.05);
    border-radius: 4px;
    padding: 15px;
    margin-bottom: 20px;
    min-height: 200px;
    color: #ffffff;
}

.selected-list ul {
    margin: 5px 0;
    padding-left: 20px;
}

.selected-list li {
    margin: 3px 0;
    font-size: 13px;
    color: #cccccc;
}

.empty-message {
    text-align: center;
    color: #888888;
    font-style: italic;
    margin: 20px 0;
}

.instructions {
    background-color: rgba(255, 255, 255, 0.05);
    border-radius: 4px;
    padding: 15px;
}

.instructions ul {
    margin: 10px 0;
    padding-left: 20px;
    color: #cccccc;
}

.instructions li {
    margin: 5px 0;
    font-size: 13px;
}

/* 响应式设计 */
@media (max-width: 768px) {
    .network-selector-modal {
        width: 95%;
        height: 95%;
    }
    
    .selector-main {
        flex-direction: column;
    }
    
    .map-container {
        height: 60%;
    }
    
    .selector-sidebar {
        height: 40%;
        min-width: unset;
        border-left: none;
        border-top: 1px solid rgba(255, 255, 255, 0.1);
    }
    
    .selector-controls {
        flex-direction: column;
        gap: 10px;
        align-items: stretch;
    }
    
    .selector-controls .info {
        justify-content: center;
    }
    
    .selector-controls .buttons {
        justify-content: center;
    }
} 

/* 嵌入式选择器容器 - 显示在页面内部而非全屏覆盖 */
.network-selector-embedded {
    width: 100%;
    height: 100%; /* 改为100%以适应父容器大小 */
    margin-top: 8px;
}

.network-selector-embedded .network-selector-modal {
    width: 100%;
    height: 100%;
    max-width: unset;
}