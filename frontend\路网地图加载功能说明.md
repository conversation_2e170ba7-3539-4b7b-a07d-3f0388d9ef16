# 路网地图加载功能实现说明

## 功能概述

在 index 页面最开始加载的时候，路网地图区域现在会自动加载底图和路网数据，显示完整的路网地图。

## 实现方案

### 1. 利用现有功能
- 发现 `script.js` 中已经有完整的地图加载功能
- 包括 `initializeMap()` 和 `initializeDefaultMap()` 函数
- 包括 `loadBackgroundImage()` 和 `loadNetwork()` 函数

### 2. 功能整合
在 `index.html` 中添加了 `initializeNetworkMapLoader()` 函数：
- 检查 Leaflet 库是否加载
- 调用 `script.js` 中已有的地图初始化函数
- 提供备用的简化地图显示方案

### 3. 全局函数暴露
在 `script.js` 中添加了全局函数暴露：
```javascript
// 将地图初始化函数暴露到全局作用域
window.initializeMap = initializeMap;
window.initializeDefaultMap = initializeDefaultMap;
```

## 主要修改文件

### 1. frontend/index.html
- 添加了 Leaflet JS 库引用
- 添加了 `initializeNetworkMapLoader()` 函数
- 在页面加载时调用地图初始化

### 2. frontend/script.js
- 将 `initializeMap` 函数暴露到全局作用域
- 将 `initializeDefaultMap` 函数暴露到全局作用域

## 功能特点

### 1. 自动加载
- 页面加载完成后自动初始化地图
- 延迟 2 秒确保所有依赖都已加载
- 自动加载底图和路网数据

### 2. 错误处理
- 检查 Leaflet 库是否加载
- 检查地图容器是否存在
- 提供备用显示方案

### 3. 兼容性
- 优先使用 `script.js` 中的完整地图功能
- 如果不可用，提供简化版本
- 不影响现有的仿真功能

## API 依赖

地图加载功能依赖以下后端 API：

### 1. 底图 API
```
GET http://localhost:8888/api/background-image
```
返回底图信息：
- image: 图片文件名
- centerX, centerY: 中心坐标
- width, height: 图片尺寸
- rotation: 旋转角度

### 2. 路网 API
```
GET http://localhost:8888/api/network
```
返回路网 GeoJSON 数据：
- features: 路网要素数组
- 包含 lane_polygon（车道）和 junction（交叉口）

## 测试页面

创建了 `frontend/test_network_map.html` 作为独立测试页面：
- 可以单独测试地图加载功能
- 提供手动控制按钮
- 显示详细的加载状态

## 使用方法

1. 确保后端服务器运行在 `http://localhost:8888`
2. 打开 `http://localhost:8888/frontend/index.html`
3. 页面加载完成后，地图区域会自动显示路网地图
4. 如果需要手动测试，可以打开 `http://localhost:8888/frontend/test_network_map.html`

## 地图配置

地图使用以下配置参数：
```javascript
const mapConfig = {
    minZoom: -5,        // 最小缩放级别
    maxZoom: 6,         // 最大缩放级别
    defaultZoom: -1,    // 默认缩放级别
    defaultCenter: [3000, 4700]  // 默认中心位置
};
```

## 样式配置

路网要素使用以下颜色配置：
```javascript
const colors = {
    road: '#4a4a4a',        // 道路填充色
    roadBorder: '#333333',  // 道路边框色
    junction: '#666666'     // 交叉口填充色
};
```

## 注意事项

1. **依赖顺序**：确保 Leaflet 库在地图初始化之前加载
2. **容器存在**：地图容器 `.map-placeholder` 必须存在
3. **API 可用**：后端 API 必须正常运行
4. **延迟加载**：使用延迟加载确保页面完全初始化

## 故障排除

### 1. 地图不显示
- 检查浏览器控制台是否有错误
- 确认 Leaflet 库是否加载成功
- 确认后端 API 是否可访问

### 2. 底图不显示
- 检查 `/api/background-image` API 是否返回正确数据
- 确认图片文件是否存在于服务器

### 3. 路网不显示
- 检查 `/api/network` API 是否返回正确的 GeoJSON 数据
- 确认数据格式是否正确

## 扩展功能

可以进一步扩展的功能：
1. 地图交互功能（点击、缩放等）
2. 图层控制（显示/隐藏不同类型的要素）
3. 地图标注和信息窗口
4. 实时数据更新
5. 地图导出功能
