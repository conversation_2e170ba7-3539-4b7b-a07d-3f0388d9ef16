// --- 脚本开头，确保全局唯一 ---
window.comparedResults = [];
// 全局数组存放所有对比方案（你可能已经有）
window.comparedResults = window.comparedResults || [];

const defaultSimulationData = {
    simulation_id: "default_sim",
    start_time: new Date().toISOString(),
    config_summary: {
        network: "默认路网",
        signal: "默认信号配置",
        traffic: "默认交通流"
    },
    simulation_results: {
        network_metrics: {
            pedestrian_metrics: {
                average_travel_time: 25,
                average_waiting_time: 3,
                average_waiting_count: 1,
                average_time_loss: 5
            },
            vehicle_metrics: {
                average_travel_time: 120,
                average_waiting_time: 40,
                average_waiting_count: 4,
                average_time_loss: 60
            },
            vip_vehicle_metrics: {
                average_travel_time: 100,
                average_waiting_time: 10,
                average_waiting_count: 1,
                average_time_loss: 20
            },
            venue_area_metrics: {
                average_pedestrian_travel_time: 10,
                average_pedestrian_delay: 2,
                average_vehicle_travel_time: 90,
                average_vehicle_delay: 15
            }
        }
    }
};


document.addEventListener('DOMContentLoaded', function() {
    updateDisplay(defaultSimulationData);
    
    // 初始化固定容器中的默认地图
    initializeDefaultMap();
    
    // 安全的元素引用函数
    function safeGetElement(id) {
        const element = document.getElementById(id);
        if (!element) {
            console.warn(`Element with id '${id}' not found`);
        }
        return element;
    }

    // 元素引用
    const netPreset = safeGetElement('net-preset');
    const netCustom = safeGetElement('net-custom');
    const uploadNetBtn = safeGetElement('uploadNetBtn');

    const trafficPreset = safeGetElement('traffic-preset');
    const trafficCustom = safeGetElement('traffic-custom');
    const uploadRouBtn = safeGetElement('uploadRouBtn');

    const vehicleType = safeGetElement('vehicleType');
    const entranceType = safeGetElement('entranceType');
    const vipPriority = safeGetElement('vipPriority');

    const signalPreset = safeGetElement('signal-preset');
    const signalCustom = safeGetElement('signal-custom');
    const uploadAddBtn = safeGetElement('uploadAddBtn');

    const roadRestriction = safeGetElement('roadRestriction');
    const signalOptimization = safeGetElement('signalOptimization');

    const runBtn = document.querySelector('.run-btn');
    const loadResultBtn = safeGetElement('loadResultBtn');
    const historyBtn = safeGetElement('historyBtn');

    // 新增：路段分析相关元素
    const edgeAnalysis = safeGetElement('edgeAnalysis');
    const edgeAnalysisDetails = safeGetElement('edgeAnalysisDetails');
    const selectEdgesBtn = safeGetElement('selectEdgesBtn');
    
    // 新增：出入口选择相关元素
    const selectEntranceBtn = safeGetElement('selectEntranceBtn');



    // 存储上传的文件路径和内容
    let uploadedNetFile = null;
    let uploadedNetFileContent = null; // 新增：存储网络文件内容
    let uploadedRouFile = null;
    let uploadedAddFile = null;

    // 存储选中的限行路段和信控优化交叉口
    let selectedRestrictedEdges = [];
    let selectedIntersections = [];
    
    // 新增：存储选中的分析路段
    let selectedAnalysisEdges = [];
    
    // 新增：存储选中的出入口路段
    let selectedEntranceEdges = [];

    // 将变量暴露到全局作用域，供index.html中的函数使用
    window.selectedRestrictedEdges = selectedRestrictedEdges;
    window.selectedIntersections = selectedIntersections;
    window.selectedAnalysisEdges = selectedAnalysisEdges;
    window.selectedEntranceEdges = selectedEntranceEdges;
    window.uploadedNetFileContent = uploadedNetFileContent; // 暴露文件内容到全局

    // 暴露仿真状态变量到全局作用域
    window.isReplaying = false;
    window.replayData = null;

    // 初始状态设置
    updateEntranceTypeState();
    updateVipPriorityState();

    // 确保内容适应屏幕高度
    // adjustContentToFitScreen();
    // window.addEventListener('resize', adjustContentToFitScreen);

    // 安全的事件监听器添加函数
    function safeAddEventListener(element, event, handler) {
        if (element && typeof element.addEventListener === 'function') {
            element.addEventListener(event, handler);
        } else {
            console.warn(`Cannot add event listener to element:`, element);
        }
    }

    // 事件监听器
    safeAddEventListener(netPreset, 'change', updateEntranceTypeState);
    safeAddEventListener(netCustom, 'change', updateEntranceTypeState);
    safeAddEventListener(vehicleType, 'change', updateVipPriorityState);

    // 上传按钮事件
    safeAddEventListener(uploadNetBtn, 'click', function() {
        if (netCustom && netCustom.checked) {
            simulateFileUpload('net');
        } else {
            showNotification('请先选择"自定义"选项', 'error');
        }
    });

    safeAddEventListener(uploadRouBtn, 'click', function() {
        if (trafficCustom && trafficCustom.checked) {
            simulateFileUpload('rou');
        } else {
            showNotification('请先选择"自定义"选项', 'error');
        }
    });

    safeAddEventListener(uploadAddBtn, 'click', function() {
        if (signalCustom && signalCustom.checked) {
            simulateFileUpload('add');
        } else {
            showNotification('请先选择"自定义"选项', 'error');
        }
    });

    // 加载已有结果按钮事件
    safeAddEventListener(loadResultBtn, 'click', loadExistingResult);

    // 限行道路按钮事件
    safeAddEventListener(roadRestriction, 'change', function() {
        if (this.checked) {
            selectRestrictedEdges();
        } else {
            selectedRestrictedEdges = [];
        }
    });

    // 信控优化按钮事件
    safeAddEventListener(signalOptimization, 'change', function() {
        if (this.checked) {
            selectIntersections();
        } else {
            selectedIntersections = [];
        }
    });

    // 新增：路段分析事件监听器
    safeAddEventListener(edgeAnalysis, 'change', function() {
        if (this.checked && edgeAnalysisDetails) {
            edgeAnalysisDetails.style.display = 'block';
        } else if (edgeAnalysisDetails) {
            edgeAnalysisDetails.style.display = 'none';
            selectedAnalysisEdges = [];
            window.selectedAnalysisEdges = selectedAnalysisEdges;
            // 清除选择状态显示
            updateSelectionStatus('edgeAnalysis', 0);
        }
    });
    // 新增：选择分析路段按钮事件
    safeAddEventListener(selectEdgesBtn, 'click', function() {
        if (edgeAnalysis && edgeAnalysis.checked) {
            selectAnalysisEdges();
        } else {
            showNotification('请先启用"用户自选路段指标分析"', 'error');
        }
    });

    // 新增：选择出入口路段按钮事件
    safeAddEventListener(selectEntranceBtn, 'click', function() {
        selectEntranceEdges();
    });

    // 运行按钮事件
    safeAddEventListener(runBtn, 'click', startSimulation);

    // 功能函数
    function updateEntranceTypeState() {
        if (selectEntranceBtn && netPreset) {
            selectEntranceBtn.disabled = !netPreset.checked;
            if (!netPreset.checked) {
                selectEntranceBtn.parentElement.parentElement.classList.add('disabled');
            } else {
                selectEntranceBtn.parentElement.parentElement.classList.remove('disabled');
            }
        }
    }

    function updateVipPriorityState() {
        if (vehicleType && vipPriority) {
            const hasVIP = vehicleType.value === 'vip';
            vipPriority.disabled = !hasVIP;
            if (!hasVIP) {
                vipPriority.checked = false;
                vipPriority.parentElement.parentElement.classList.add('disabled');
            } else {
                vipPriority.parentElement.parentElement.classList.remove('disabled');
            }
        }
    }
    // 更新对比方案名称栏
function updateComparisonSchemesInfo() {
    const infoDiv = document.getElementById('comparisonSchemesInfo');
    if (!infoDiv) return;
    const compared = window.comparedResults || [];
    if (compared.length === 0) {
        infoDiv.textContent = "当前对比方案：无";
    } else {
        const schemeNames = compared.map((item, idx) =>
            item.name ? `${item.name}（${item.id}）` : `方案${idx+1}（${item.id}）`
        ).join('、');
        infoDiv.textContent = `当前对比方案：${schemeNames}`;
    }
}


    function adjustContentToFitScreen() {
        const container = document.querySelector('.container');
        const windowHeight = window.innerHeight;
        const contentHeight = container.scrollHeight;

        // 如果内容高度超过窗口高度，调整容器的缩放比例
        if (contentHeight > windowHeight) {
            const scale = Math.min(0.95, windowHeight / contentHeight);
            container.style.transform = `scale(${scale})`;
            container.style.transformOrigin = 'top center';
            container.style.marginBottom = `${(windowHeight - contentHeight * scale) / 2}px`;
        } else {
            container.style.transform = '';
            container.style.marginBottom = '';
        }
    }

    function simulateFileUpload(fileType) {
        const input = document.createElement('input');
        input.type = 'file';

        let fileExtension = '';
        let acceptTypes = '';

        switch(fileType) {
            case 'net':
                fileExtension = '.net.xml';
                acceptTypes = '.net.xml';
                break;
            case 'rou':
                fileExtension = '.rou.xml';
                acceptTypes = '.rou.xml';
                break;
            case 'add':
                fileExtension = '.add.xml';
                acceptTypes = '.add.xml';
                break;
        }

        input.accept = acceptTypes;

        input.onchange = function(e) {
            const file = e.target.files[0];
            if (file) {
                // 验证文件扩展名
                if (!file.name.toLowerCase().endsWith(fileExtension)) {
                    showNotification(`文件类型错误，请选择${fileExtension}文件`, 'error');
                    return;
                }

                // 验证文件内容（这里只是模拟，实际中可以读取文件内容进行更深入的验证）
                validateFileContent(file, fileType)
                    .then(isValid => {
                        if (isValid) {
                            // 处理文件存储
                            switch(fileType) {
                                case 'net':
                                    uploadedNetFile = file.name;
                                    // 读取网络文件内容，供路段选择器使用
                                    const reader = new FileReader();
                                    reader.onload = function(e) {
                                        uploadedNetFileContent = e.target.result;
                                        window.uploadedNetFileContent = uploadedNetFileContent; // 同步更新全局变量
                                        console.log('网络文件内容已读取完成');
                                    };
                                    reader.readAsText(file);
                                    break;
                                case 'rou':
                                    uploadedRouFile = file.name;
                                    break;
                                case 'add':
                                    uploadedAddFile = file.name;
                                    break;
                            }

                            showNotification(`已选择文件: ${file.name}`, 'success');
                        } else {
                            showNotification(`文件内容无效，请选择有效的${fileExtension}文件`, 'error');
                        }
                    })
                    .catch(error => {
                        showNotification('文件验证过程中发生错误', 'error');
                        console.error(error);
                    });
            }
        };

        input.click();
    }

    // 加载已有结果文件
    function loadExistingResult() {
        const input = document.createElement('input');
        input.type = 'file';
        input.accept = '.json';

        input.onchange = function(e) {
            const file = e.target.files[0];
            if (file) {
                // 验证文件扩展名
                if (!file.name.toLowerCase().endsWith('.json')) {
                    showNotification('文件类型错误，请选择JSON格式文件', 'error');
                    return;
                }

                // 读取JSON文件内容
                const reader = new FileReader();
                reader.onload = function(e) {
                    try {
                        // 尝试解析JSON
                        const jsonData = JSON.parse(e.target.result);

                        // 验证JSON结构
                        if (validateResultJson(jsonData)) {
                            showNotification('正在加载结果文件...', 'info');

                            // 转换为统一格式（新格式）
                            const normalizedData = normalizeResultJson(jsonData);

                            // 如果没有config_summary，从config生成简化摘要
                            if (!normalizedData.config_summary && normalizedData.config) {
                                normalizedData.config_summary = generateConfigSummaryFromConfig(normalizedData.config);
                            }

                            // 如果没有start_time，使用当前时间
                            if (!normalizedData.start_time) {
                                normalizedData.start_time = new Date().toISOString();
                            }

                            // 直接在当前页面显示结果，而不是跳转
                            updateDisplay(normalizedData);
                            showNotification('结果已加载', 'success');
                        } else {
                            showNotification('JSON格式无效，请提供有效的结果文件', 'error');
                        }
                    } catch (error) {
                        showNotification('无法解析JSON文件', 'error');
                        console.error('JSON解析错误:', error);
                    }
                };

                reader.onerror = function() {
                    showNotification('读取文件时发生错误', 'error');
                };

                reader.readAsText(file);
            }
        };

        input.click();
    }

    // 验证结果JSON的结构 - 支持新旧格式兼容
    function validateResultJson(json) {
        console.log('验证JSON结构:', json);

        // 检查必要的字段是否存在
        const requiredFields = ['simulation_id', 'simulation_results'];
        const missingFields = [];

        for (const field of requiredFields) {
            if (!json[field]) {
                missingFields.push(field);
            }
        }

        if (missingFields.length > 0) {
            console.error('缺少必要字段:', missingFields);
            console.log('JSON包含的字段:', Object.keys(json));
            return false;
        }

        // 检查仿真结果字段
        const results = json.simulation_results;
        if (!results) {
            console.error('simulation_results字段不存在');
            return false;
        }

        // 检查是否为新格式（包含network_metrics）
        if (results.network_metrics) {
            console.log('检测到新格式JSON');
            const networkMetrics = results.network_metrics;

            // 验证新格式的必要字段
            if (!networkMetrics.pedestrian_metrics && !networkMetrics.vehicle_metrics) {
                console.error('新格式中network_metrics缺少行人和车辆指标');
                return false;
            }

            // 进一步验证network_metrics的必要字段
            if (!networkMetrics.pedestrian_metrics || !networkMetrics.vehicle_metrics) {
                console.error('network_metrics中缺少行人或车辆指标:', {
                    pedestrian_metrics: !!networkMetrics.pedestrian_metrics,
                    vehicle_metrics: !!networkMetrics.vehicle_metrics
                });
                console.log('network_metrics包含的字段:', Object.keys(networkMetrics));
                return false;
            }

            // 检查是否包含路段分析结果（可选）
            if (results.selected_edge_metrics) {
                console.log('包含路段分析结果');
            }
        } else {
            // 检查是否为旧格式（直接包含指标）
            console.log('检测到旧格式JSON，尝试验证...');

            // 旧格式至少需要包含行人或车辆指标之一
            if (!results.pedestrian_metrics && !results.vehicle_metrics) {
                console.error('旧格式中缺少行人和车辆指标');
                console.log('simulation_results包含的字段:', Object.keys(results));
                return false;
            }

            console.log('旧格式JSON验证通过');
        }

        console.log('JSON验证通过');
        return true;
    }

    // 将结果JSON标准化为新格式
    function normalizeResultJson(json) {
        console.log('标准化JSON格式:', json);

        // 如果已经是新格式，直接返回
        if (json.simulation_results && json.simulation_results.network_metrics) {
            console.log('已是新格式，无需转换');
            return json;
        }

        // 转换旧格式为新格式
        console.log('转换旧格式为新格式');
        const normalizedJson = {
            simulation_id: json.simulation_id,
            config: json.config || {},
            config_summary: json.config_summary,
            start_time: json.start_time,
            simulation_results: {
                network_metrics: {
                    pedestrian_metrics: json.simulation_results.pedestrian_metrics || {},
                    vehicle_metrics: json.simulation_results.vehicle_metrics || {},
                    vip_vehicle_metrics: json.simulation_results.vip_vehicle_metrics || {},
                    venue_area_metrics: json.simulation_results.venue_area_metrics || {}
                }
            }
        };

        // 如果存在路段分析结果，也复制过来
        if (json.simulation_results.selected_edge_metrics) {
            normalizedJson.simulation_results.selected_edge_metrics = json.simulation_results.selected_edge_metrics;
        }

        console.log('格式转换完成:', normalizedJson);
        return normalizedJson;
    }

    // 从完整config生成简化的配置摘要
    function generateConfigSummaryFromConfig(config) {
        const summary = {
            network: '未知',
            signal: '未知',
            traffic: '未知'
        };

        try {
            // 提取路网信息
            if (config.network_config) {
                if (config.network_config.type === 'predefined') {
                    summary.network = '预设路网';
                } else {
                    summary.network = '自定义路网';
                }

                // 添加出入口信息
                if (config.network_config.entrance_plan) {
                    summary.network += ` (${config.network_config.entrance_plan})`;
                }
            }

            // 提取信号灯信息
            if (config.signal_config) {
                if (config.signal_config.optimization && config.signal_config.optimization.enabled) {
                    summary.signal = '启用优化';
                } else {
                    summary.signal = '未优化';
                }
            }

            // 提取交通配置信息
            const measures = [];
            if (config.traffic_config) {
                if (config.traffic_config.scenario) {
                    measures.push(config.traffic_config.scenario);
                }
                if (config.traffic_config.vehicle_type) {
                    measures.push(config.traffic_config.vehicle_type);
                }
                if (config.traffic_config.vip_priority && config.traffic_config.vip_priority.enabled) {
                    measures.push('贵宾优先');
                }
            }

            // 检查道路限行
            if (config.network_config && config.network_config.road_restriction && config.network_config.road_restriction.enabled) {
                measures.push('道路限行');
            }

            summary.traffic = measures.length > 0 ? measures.join(', ') : '无特殊配置';

        } catch (error) {
            console.warn('生成配置摘要时出错:', error);
        }

        return summary;
    }

    function validateFileContent(file, fileType) {
        return new Promise((resolve, reject) => {
            const reader = new FileReader();

            reader.onload = function(e) {
                const content = e.target.result;

                // 简单检查XML格式和关键标签
                let isValid = content.trim().startsWith('<?xml');

                if (isValid) {
                    switch(fileType) {
                        case 'net':
                            isValid = content.includes('<net') || content.includes('<network');
                            break;
                        case 'rou':
                            isValid = content.includes('<routes') || content.includes('<flows');
                            break;
                        case 'add':
                            isValid = content.includes('<additional') || content.includes('<additionals');
                            break;
                    }
                }

                resolve(isValid);
            };

            reader.onerror = function() {
                reject(new Error('文件读取错误'));
            };

            reader.readAsText(file);
        });
    }

    function getCurrentNetFilePath() {
        // 返回当前使用的路网文件路径
        if (netCustom.checked && uploadedNetFile) {
            return 'CUSTOM_FILE:' + uploadedNetFile; // 使用特殊前缀标识自定义文件
        } else {
            // 返回预设路网路径（根据出入口选择不同的预设文件）
            return 'sumo_data/templates/gym_tls.net.xml'; 
        }
    }

    // 选择分析路段
    async function selectAnalysisEdges() {
        // 首先获取当前路网文件路径
        const netFilePath = getCurrentNetFilePath();
        if (!netFilePath) {
            showError('请先选择或上传路网文件');
            edgeAnalysis.checked = false;
            return;
        }

        try {
            // 显示加载中
            showLoading('正在加载路网数据...');

            // 解析路网文件
            await networkSelector.parseNetworkFile(netFilePath);

            hideLoading();

            // 获取嵌入式选择器容器
            const embeddedView = document.getElementById('embedded-selector-view');
            if (!embeddedView) {
                showError('找不到嵌入式选择器容器');
                return;
            }

            // 隐藏默认地图视图，显示嵌入式选择器容器
            const defaultMapView = document.getElementById('default-map-view');
            if (defaultMapView) defaultMapView.style.display = 'none';
            const mapView = document.getElementById('map-view');
            if (mapView) mapView.style.display = 'none';
            embeddedView.style.display = 'block';
            embeddedView.innerHTML = ''; // 清空之前的内容

            // 显示选择器（嵌入模式）
            networkSelector.showSelector('analysis', (selectedIds) => {
                // 更新选中的路段列表
                selectedAnalysisEdges = selectedIds;

                // 更新全局变量
                window.selectedAnalysisEdges = selectedAnalysisEdges;

                // 更新选择状态显示
                updateSelectionStatus('edgeAnalysis', selectedAnalysisEdges.length);

                // 显示成功通知
                showNotification(`已选择 ${selectedAnalysisEdges.length} 个路段用于分析`, 'success');

                // 恢复地图视图
                embeddedView.style.display = 'none';
                // 延迟恢复普通地图视图，避免与仿真冲突
                setTimeout(() => {
                    initializeDefaultMap();
                }, 100);
            }, embeddedView);

        } catch (error) {
            console.error('启动路段选择器失败:', error);
            showError('启动路段选择器失败: ' + error.message);
            edgeAnalysis.checked = false;
            hideLoading();
            
            // 恢复默认地图视图
            const embeddedView = document.getElementById('embedded-selector-view');
            if (embeddedView) embeddedView.style.display = 'none';
            initializeDefaultMap();
        }
    }

    // 选择出入口路段
    async function selectEntranceEdges() {
        // 首先获取当前路网文件路径
        const netFilePath = getCurrentNetFilePath();
        if (!netFilePath) {
            showError('请先选择或上传路网文件');
            return;
        }

        try {
            // 显示加载中
            showLoading('正在加载路网数据...');

            // 解析路网文件
            await networkSelector.parseNetworkFile(netFilePath);

            hideLoading();

            // 获取嵌入式选择器容器
            const embeddedView = document.getElementById('embedded-selector-view');
            if (!embeddedView) {
                showError('找不到嵌入式选择器容器');
                return;
            }

            // 隐藏默认地图视图，显示嵌入式选择器容器
            const defaultMapView = document.getElementById('default-map-view');
            if (defaultMapView) defaultMapView.style.display = 'none';
            const mapView = document.getElementById('map-view');
            if (mapView) mapView.style.display = 'none';
            embeddedView.style.display = 'block';
            embeddedView.innerHTML = ''; // 清空之前的内容

            // 显示选择器（嵌入模式）
            networkSelector.showSelector('entrance', (selectedIds) => {
                // 更新选中的路段列表
                selectedEntranceEdges = selectedIds;

                // 更新全局变量
                window.selectedEntranceEdges = selectedEntranceEdges;

                // 更新选择状态显示
                updateEntranceSelectionStatus(selectedEntranceEdges.length);

                // 显示成功通知
                showNotification(`已选择 ${selectedEntranceEdges.length} 个出入口路段`, 'success');

                // 恢复地图视图
                embeddedView.style.display = 'none';
                // 延迟恢复普通地图视图，避免与仿真冲突
                setTimeout(() => {
                    initializeDefaultMap();
                }, 100);
            }, embeddedView);

        } catch (error) {
            console.error('启动出入口路段选择器失败:', error);
            showError('启动出入口路段选择器失败: ' + error.message);
            hideLoading();
            
            // 恢复默认地图视图
            const embeddedView = document.getElementById('embedded-selector-view');
            if (embeddedView) embeddedView.style.display = 'none';
            initializeDefaultMap();
        }
    }

    // 选择限行道路
    async function selectRestrictedEdges() {
        // 首先获取当前路网文件路径
        const netFilePath = getCurrentNetFilePath();
        if (!netFilePath) {
            showError('请先选择或上传路网文件');
            roadRestriction.checked = false;
            return;
        }

        try {
            // 显示加载中
            showLoading('正在加载路网数据...');

            // 解析路网文件
            await networkSelector.parseNetworkFile(netFilePath);

            hideLoading();

            // 获取嵌入式选择器容器
            const embeddedView = document.getElementById('embedded-selector-view');
            if (!embeddedView) {
                showError('找不到嵌入式选择器容器');
                return;
            }

            // 隐藏默认地图视图，显示嵌入式选择器容器
            const defaultMapView = document.getElementById('default-map-view');
            if (defaultMapView) defaultMapView.style.display = 'none';
            const mapView = document.getElementById('map-view');
            if (mapView) mapView.style.display = 'none';
            embeddedView.style.display = 'block';
            embeddedView.innerHTML = ''; // 清空之前的内容

            // 显示选择器（嵌入模式）
            networkSelector.showSelector('restriction', (selectedIds) => {
                // 更新选中的路段列表
                selectedRestrictedEdges = selectedIds;

                // 更新全局变量
                window.selectedRestrictedEdges = selectedRestrictedEdges;

                // 更新选择状态显示
                updateSelectionStatus('roadRestriction', selectedRestrictedEdges.length);

                // 显示成功通知
                showNotification(`已选择 ${selectedRestrictedEdges.length} 个限行路段`, 'success');

                // 恢复地图视图
                embeddedView.style.display = 'none';
                // 延迟恢复普通地图视图，避免与仿真冲突
                setTimeout(() => {
                    initializeDefaultMap();
                }, 100);
            }, embeddedView);

        } catch (error) {
            console.error('启动限行路段选择器失败:', error);
            showError('启动限行路段选择器失败: ' + error.message);
            roadRestriction.checked = false;
            hideLoading();
            
            // 恢复默认地图视图
            const embeddedView = document.getElementById('embedded-selector-view');
            if (embeddedView) embeddedView.style.display = 'none';
            initializeDefaultMap();
        }
    }

    // 选择信控优化交叉口
    async function selectIntersections() {
        // 首先获取当前路网文件路径
        const netFilePath = getCurrentNetFilePath();
        if (!netFilePath) {
            showError('请先选择或上传路网文件');
            signalOptimization.checked = false;
            return;
        }

        try {
            // 显示加载中
            showLoading('正在加载路网数据...');

            // 解析路网文件
            await networkSelector.parseNetworkFile(netFilePath);

            hideLoading();

            // 获取嵌入式选择器容器
            const embeddedView = document.getElementById('embedded-selector-view');
            if (!embeddedView) {
                showError('找不到嵌入式选择器容器');
                return;
            }

            // 隐藏默认地图视图，显示嵌入式选择器容器
            const defaultMapView = document.getElementById('default-map-view');
            if (defaultMapView) defaultMapView.style.display = 'none';
            const mapView = document.getElementById('map-view');
            if (mapView) mapView.style.display = 'none';
            embeddedView.style.display = 'block';
            embeddedView.innerHTML = ''; // 清空之前的内容

            // 显示选择器（嵌入模式）
            networkSelector.showSelector('intersection', (selectedIds) => {
                // 处理选择结果（仅交叉口）
                if (selectedIds && typeof selectedIds === 'object' && selectedIds.intersections) {
                    selectedIntersections = selectedIds.intersections;
                    
                    // 更新全局变量
                    window.selectedIntersections = selectedIntersections;

                    // 更新选择状态显示
                    updateSelectionStatus('signalOptimization', selectedIntersections.length);

                    // 显示成功通知
                    showNotification(`已选择 ${selectedIntersections.length} 个交叉口进行信控优化`, 'success');
                } else {
                    // 兼容旧格式（仅交叉口）
                    selectedIntersections = selectedIds || [];
                    
                    // 更新全局变量
                    window.selectedIntersections = selectedIntersections;

                    // 更新选择状态显示
                    updateSelectionStatus('signalOptimization', selectedIntersections.length);

                    // 显示成功通知
                    showNotification(`已选择 ${selectedIntersections.length} 个交叉口进行信控优化`, 'success');
                }
                
                // 恢复地图视图
                embeddedView.style.display = 'none';
                // 延迟恢复普通地图视图，避免与仿真冲突
                setTimeout(() => {
                    initializeDefaultMap();
                }, 100);
            }, embeddedView);

        } catch (error) {
            console.error('启动交叉口选择器失败:', error);
            showError('启动交叉口选择器失败: ' + error.message);
            signalOptimization.checked = false;
            hideLoading();
            
            // 恢复默认地图视图
            const embeddedView = document.getElementById('embedded-selector-view');
            if (embeddedView) embeddedView.style.display = 'none';
            initializeDefaultMap();
        }
    }

    // 更新选择状态显示
    function updateSelectionStatus(elementId, count) {
        // 获取对应的状态元素
        const statusElement = document.getElementById(`${elementId}Status`);
        if (statusElement) {
            if (count > 0) {
                statusElement.textContent = `(已选择 ${count} 项)`;
                statusElement.style.display = 'inline';
                statusElement.style.color = '#4caf50';
                statusElement.style.marginLeft = '5px';
                statusElement.style.fontSize = '0.9em';
            } else {
                statusElement.style.display = 'none';
            }
        }
    }

    // 更新出入口选择状态UI
    function updateEntranceSelectionStatus(count) {
        const statusElement = document.getElementById('entranceSelectionStatus');
        if (!statusElement) return;

        if (count > 0) {
            statusElement.textContent = `(已选择 ${count} 个路段)`;
            statusElement.style.display = 'inline';
        } else {
            statusElement.style.display = 'none';
        }
    }

    // ==================== 地图加载功能 ====================

    // 地图加载相关变量
    let map = null;
    let backgroundImageLayer = null;

    // 地图配置参数
    const mapConfig = {
        // 缩放级别配置
        minZoom: -5,        // 最小缩放级别（可以缩得更小）
        maxZoom: 6,         // 最大缩放级别
        defaultZoom: -1,    // 默认缩放级别

        // 默认中心位置
        defaultCenter: [3000, 4700],

        // 加载完成后的视图配置
        fitBoundsPadding: 0.1,  // 边界填充比例
        autoFitBounds: false    // 禁用自动适应，使用defaultZoom和defaultCenter
    };

    // 颜色配置
    const colors = {
        road: '#4a4a4a',
        roadBorder: '#333333',
        junction: '#666666'
    };

    // 加载底图
    function loadBackgroundImage() {
        updateMapStatus('正在加载底图...');

        fetch('http://localhost:8888/api/background-image')
            .then(response => response.json())
            .then(data => {
                if (data.image) {
                    // 移除现有的底图（如果有）
                    if (backgroundImageLayer) {
                        backgroundImageLayer.remove();
                    }

                    // 计算底图边界
                    const centerX = data.centerX || 0;
                    const centerY = data.centerY || 0;
                    const width = data.width || 1000;
                    const height = data.height || 1000;
                    const rotation = data.rotation || 0;

                    // 计算图像边界
                    const halfWidth = width / 2;
                    const halfHeight = height / 2;

                    // 计算图像四个角的坐标（使用简单的矩形）
                    const southWest = map.unproject([centerX - halfWidth, -(centerY + halfHeight)], 0);
                    const northEast = map.unproject([centerX + halfWidth, -(centerY - halfHeight)], 0);
                    const bounds = L.latLngBounds(southWest, northEast);

                    // 创建图像覆盖层
                    const imageUrl = `http://localhost:8888/files/${data.image}`;
                    backgroundImageLayer = L.imageOverlay(imageUrl, bounds, {
                        opacity: 0.8,
                        interactive: false
                    }).addTo(map);

                    // 如果需要旋转
                    if (rotation !== 0) {
                        // 获取图像元素并应用旋转
                        backgroundImageLayer.on('load', function() {
                            const imgElement = backgroundImageLayer.getElement();
                            if (imgElement) {
                                // 设置旋转原点为图像中心
                                imgElement.style.transformOrigin = 'center center';
                                // 应用旋转（角度制）
                                imgElement.style.transform = `rotate(${rotation * 180 / Math.PI}deg)`;
                            }
                        });
                    }

                    updateMapStatus('底图加载完成');

                    map.setView(mapConfig.defaultCenter, mapConfig.defaultZoom);

                    // 加载路网
                    loadNetwork();
                }
            })
            .catch(error => {
                console.error('加载底图失败:', error);
                updateMapStatus('底图加载失败');
            });
    }

    // 加载路网数据
    function loadNetwork() {
        updateMapStatus('正在加载路网...');

        fetch('http://localhost:8888/api/network')
            .then(response => response.json())
            .then(data => {
                if (data.features && data.features.length > 0) {
                    // 分离不同类型的要素
                    const lanePolygons = data.features.filter(f => f.properties.type === 'lane_polygon');
                    const lanes = data.features.filter(f => f.properties.type === 'lane');
                    const junctions = data.features.filter(f => f.properties.type === 'junction');

                    // 先添加车道多边形
                    if (lanePolygons.length > 0) {
                        const lanePolygonLayer = L.geoJSON({
                            type: "FeatureCollection",
                            features: lanePolygons
                        }, {
                            coordsToLatLng: function(coords) {
                                return map.unproject([coords[0], -coords[1]], 0);
                            },
                            style: function(feature) {
                                return {
                                    color: colors.roadBorder,
                                    weight: 1,
                                    opacity: 1,
                                    fillColor: colors.road,
                                    fillOpacity: 0.8
                                };
                            }
                        }).addTo(map);
                    }

                    // 然后添加交叉口
                    if (junctions.length > 0) {
                        const junctionLayer = L.geoJSON({
                            type: "FeatureCollection",
                            features: junctions
                        }, {
                            coordsToLatLng: function(coords) {
                                return map.unproject([coords[0], -coords[1]], 0);
                            },
                            style: function(feature) {
                                return {
                                    color: "#000000",
                                    weight: 1,
                                    opacity: 1,
                                    fillColor: colors.junction,
                                    fillOpacity: 0.6
                                };
                            }
                        }).addTo(map);
                    }

                    updateMapStatus('路网加载完成');
                    hideLoading();
                }
            })
            .catch(error => {
                console.error('加载路网失败:', error);
                updateMapStatus('路网加载失败');
                hideLoading();
            });
    }

    // 更新地图状态显示
    function updateMapStatus(message) {
        console.log('地图状态:', message);
        // 这里可以添加状态显示逻辑
    }

    // 初始化地图
    function initializeMap() {
        // 获取地图容器
        const mapContainer = document.getElementById('default-map-view');
        if (!mapContainer) {
            console.error('找不到地图容器');
            return;
        }

        // 清空容器内容
        mapContainer.innerHTML = '';

        // 创建地图div
        const mapDiv = document.createElement('div');
        mapDiv.id = 'simulation-map';
        mapDiv.style.width = '100%';
        mapDiv.style.height = '100%';
        mapContainer.appendChild(mapDiv);

        // 初始化Leaflet地图
        map = L.map('simulation-map', {
            crs: L.CRS.Simple,
            minZoom: mapConfig.minZoom,
            maxZoom: mapConfig.maxZoom,
            zoomControl: true
        });

        // 设置初始视图
        map.setView(mapConfig.defaultCenter, mapConfig.defaultZoom);

        console.log('地图初始化完成');

        // 开始加载底图
        loadBackgroundImage();
    }

    // 将地图初始化函数暴露到全局作用域
    window.initializeMap = initializeMap;

    // 开始仿真
    async function startSimulation() {
        try {
            // 构建配置JSON
            const config = window.buildConfigJSON ? window.buildConfigJSON() : getDefaultConfig();
            console.log('构建的配置:', config);

            // 验证配置
            if (!validateConfig(config)) {
                return;
            }

            // 第一步：清理网络选择器状态并准备仿真地图
            showLoading('正在准备仿真环境...');

            // 重置网络选择器状态，避免与SUMO Web冲突
            if (window.networkSelector) {
                window.networkSelector.reset();
                // 隐藏网络选择器地图，确保不会干扰仿真地图
                window.networkSelector.hideMap();
            }

            // 确保嵌入式选择器视图被隐藏
            const embeddedView = document.getElementById('embedded-selector-view');
            if (embeddedView) {
                embeddedView.style.display = 'none';
            }

            // 确保默认地图视图可见，用于仿真显示
            const defaultMapView = document.getElementById('default-map-view');
            if (defaultMapView) {
                defaultMapView.style.display = 'flex';
            }

            // 第二步：初始化仿真地图
            showLoading('正在初始化仿真地图...');
            initializeMap();

            // 第三步：启动仿真计算
            showLoading('正在进行仿真计算...');

            const response = await fetch('http://localhost:8888/api/start_simulation', {
                method: 'POST',
                headers: { 'Content-Type': 'application/json' },
                body: JSON.stringify(config),
                timeout: 300000 // 5分钟超时
            });

            const result = await response.json();

            if (result.success) {
                // 显示结果
                updateDisplay(result.data);
                showNotification('仿真计算完成', 'success');

                // 启动replay模式播放
                const simulationId = result.data.simulation_id;
                showLoading('正在加载仿真数据...');

                try {
                    await startReplayMode(simulationId);
                    hideLoading();
                    showNotification('仿真播放已开始', 'success');
                } catch (replayError) {
                    hideLoading();
                    console.error('启动replay模式失败:', replayError);
                    showError('仿真数据播放失败: ' + replayError.message);
                }
            } else {
                hideLoading();
                showError(result.message || '仿真计算失败');
            }
        } catch (error) {
            console.error('仿真启动错误:', error);
            hideLoading();
            showError('仿真启动失败: ' + error.message);
        }
    }

    // ==================== 仿真回放功能 ====================

    let replayData = null;
    let replayInterval = null;
    let currentFrame = 0;
    let isReplaying = false;

    // 启动回放模式
    async function startReplayMode(simulationId) {
        try {
            console.log('开始加载仿真回放数据:', simulationId);

            // 获取回放数据
            const response = await fetch(`http://localhost:8888/api/simulation/${simulationId}/replay_data`);

            if (!response.ok) {
                throw new Error(`获取回放数据失败: ${response.status}`);
            }

            replayData = await response.json();
            window.replayData = replayData; // 同步到全局变量
            console.log('回放数据加载完成:', replayData);

            if (!replayData.frames || replayData.frames.length === 0) {
                throw new Error('回放数据为空');
            }

            // 检查地图是否已经初始化
            if (!map) {
                console.error('地图未初始化，无法启动回放');
                throw new Error('地图未初始化，无法启动回放');
            }

            console.log('使用已初始化的地图启动回放模式');

            // 确保地图容器可见且正确设置
            const defaultMapView = document.getElementById('default-map-view');
            const embeddedView = document.getElementById('embedded-selector-view');

            if (defaultMapView) {
                defaultMapView.style.display = 'flex';
            }
            if (embeddedView) {
                embeddedView.style.display = 'none';
            }

            // 初始化回放状态
            currentFrame = 0;
            isReplaying = false;

            // 清除之前的回放实体
            clearReplayEntities();

            // 显示回放控制界面
            showReplayControls();

            // 显示第一帧
            displayFrame(replayData.frames[0]);

            console.log(`回放数据准备完成，共 ${replayData.frames.length} 帧`);

        } catch (error) {
            console.error('启动回放模式失败:', error);
            throw error;
        }
    }

    // 显示回放控制界面
    function showReplayControls() {
        // 检查是否已存在控制界面
        let controlsDiv = document.getElementById('replay-controls');
        if (!controlsDiv) {
            controlsDiv = document.createElement('div');
            controlsDiv.id = 'replay-controls';
            controlsDiv.style.cssText = `
                position: absolute;
                bottom: 10px;
                left: 10px;
                right: 10px;
                background: rgba(0, 0, 0, 0.8);
                color: white;
                padding: 10px;
                border-radius: 8px;
                display: flex;
                align-items: center;
                gap: 15px;
                z-index: 1000;
                font-size: 14px;
            `;

            controlsDiv.innerHTML = `
                <button id="replay-play-pause" onclick="toggleReplay()"
                        style="padding: 5px 10px; border: none; border-radius: 4px; background: #007cba; color: white; cursor: pointer;">播放</button>
                <button id="replay-stop" onclick="stopReplay()"
                        style="padding: 5px 10px; border: none; border-radius: 4px; background: #dc3545; color: white; cursor: pointer;">停止</button>
                <span id="replay-info" style="min-width: 100px;">帧: 0 / 0</span>
                <label style="display: flex; align-items: center; gap: 5px;">
                    速度:
                    <input type="range" id="replay-speed" min="50" max="1000" value="200"
                           style="width: 120px;" onchange="changeReplaySpeed(this.value)">
                    <span id="speed-display" style="min-width: 50px;">200ms</span>
                </label>
            `;

            // 将控制界面添加到地图容器内
            const mapContainer = document.getElementById('default-map-view');
            if (mapContainer) {
                // 确保地图容器有相对定位
                mapContainer.style.position = 'relative';
                mapContainer.appendChild(controlsDiv);
            } else {
                // 如果找不到地图容器，回退到body
                document.body.appendChild(controlsDiv);
            }
        }

        // 更新控制界面
        updateReplayControls();
    }

    // 更新回放控制界面
    function updateReplayControls() {
        if (!replayData) return;

        const info = document.getElementById('replay-info');

        if (info) {
            info.textContent = `帧: ${currentFrame + 1} / ${replayData.frames.length}`;
        }
    }

    // 切换播放/暂停
    window.toggleReplay = function() {
        if (isReplaying) {
            pauseReplay();
        } else {
            startReplayPlayback();
        }
    }

    // 开始播放
    function startReplayPlayback() {
        if (!replayData || replayData.frames.length === 0) return;

        isReplaying = true;
        window.isReplaying = true; // 同步到全局变量
        document.getElementById('replay-play-pause').textContent = '暂停';

        const intervalMs = parseInt(document.getElementById('replay-speed').value);

        replayInterval = setInterval(() => {
            if (currentFrame >= replayData.frames.length - 1) {
                // 播放完成
                pauseReplay();
                return;
            }

            currentFrame++;
            displayFrame(replayData.frames[currentFrame]);
            updateReplayControls();
        }, intervalMs);
    }

    // 暂停播放
    function pauseReplay() {
        isReplaying = false;
        window.isReplaying = false; // 同步到全局变量
        document.getElementById('replay-play-pause').textContent = '播放';

        if (replayInterval) {
            clearInterval(replayInterval);
            replayInterval = null;
        }
    }

    // 停止播放
    window.stopReplay = function() {
        pauseReplay();
        currentFrame = 0;
        if (replayData && replayData.frames.length > 0) {
            displayFrame(replayData.frames[0]);
        } else {
            // 如果没有数据，清除所有实体
            clearReplayEntities();
        }
        updateReplayControls();

        // 清除仿真数据，允许重新加载默认地图
        replayData = null;
        window.replayData = null;
    }

    // 改变播放速度
    window.changeReplaySpeed = function(intervalMs) {
        // 更新速度显示
        const speedDisplay = document.getElementById('speed-display');
        if (speedDisplay) {
            speedDisplay.textContent = intervalMs + 'ms';
        }

        // 如果正在播放，重新启动以应用新速度
        if (isReplaying) {
            pauseReplay();
            startReplayPlayback();
        }
    }

    // 显示指定帧的数据
    function displayFrame(frameData) {
        if (!frameData || !frameData.entities) return;

        console.log(`显示第 ${frameData.step} 帧，实体数量: ${frameData.entities.length}`);

        // 在地图上显示实体
        updateReplayEntities(frameData.entities);
    }

    // 回放模式的实体更新函数（参考SUMOWeb的updateEntities）
    let replayEntityMarkers = {};  // 回放模式的实体标记

    function updateReplayEntities(entities) {
        console.log('updateReplayEntities called with', entities.length, 'entities');
        console.log('map object:', map);

        if (!map) {
            console.warn('地图未初始化，无法显示实体');
            return;
        }

        // 检查地图容器是否可见
        const mapContainer = document.getElementById('default-map-view');
        const mapDiv = document.getElementById('simulation-map');
        console.log('地图容器状态:', {
            mapContainer: mapContainer ? 'exists' : 'missing',
            mapDiv: mapDiv ? 'exists' : 'missing',
            mapContainerDisplay: mapContainer ? mapContainer.style.display : 'N/A',
            mapDivDisplay: mapDiv ? mapDiv.style.display : 'N/A'
        });

        const seenEntities = new Set();

        // 使用requestAnimationFrame批量更新DOM
        requestAnimationFrame(() => {
            entities.forEach(entity => {
                seenEntities.add(entity.id);

                // 转换坐标：我们的数据格式是[x, y]，需要转换为地图坐标
                let latLng;
                if (entity.position && entity.position.length >= 2) {
                    // 使用Simple CRS，需要将y坐标取反
                    latLng = map.unproject([entity.position[0], -entity.position[1]], 0);
                } else {
                    console.warn('实体坐标数据无效:', entity);
                    return;
                }

                if (replayEntityMarkers[entity.id]) {
                    // 更新现有实体位置和图标（速度可能变化）
                    replayEntityMarkers[entity.id].setLatLng(latLng);

                    // 根据新的速度更新图标
                    if (entity.type === 'person') {
                        replayEntityMarkers[entity.id].setIcon(createReplayPersonIcon(entity.speed || 0));
                    } else if (entity.type === 'vehicle') {
                        const isVip = entity.id === 'vip' || entity.id.includes('vip');
                        replayEntityMarkers[entity.id].setIcon(createReplayVehicleIcon(isVip, entity.speed || 0));
                    }
                } else {
                    // 创建新实体
                    let marker;
                    if (entity.type === 'person') {
                        marker = L.marker(latLng, {
                            icon: createReplayPersonIcon(entity.speed || 0)
                        }).addTo(map);
                    } else if (entity.type === 'vehicle') {
                        // 根据是否为VIP车辆选择不同图标（只通过ID判断）
                        const isVip = entity.id === 'vip' || entity.id.includes('vip');

                        // 调试信息：显示实体类型和速度
                        if (isVip) {
                            console.log(`VIP车辆: ${entity.id}, 速度: ${entity.speed}`);
                        }

                        marker = L.marker(latLng, {
                            icon: createReplayVehicleIcon(isVip, entity.speed || 0)
                        }).addTo(map);
                    }

                    if (marker) {
                        replayEntityMarkers[entity.id] = marker;
                    }
                }
            });

            // 移除离开的实体
            Object.keys(replayEntityMarkers).forEach(id => {
                if (!seenEntities.has(id)) {
                    replayEntityMarkers[id].remove();
                    delete replayEntityMarkers[id];
                }
            });
        });
    }

    // 根据速度计算颜色（绿到红的线性插值）
    function getSpeedColor(speed, maxSpeed) {
        // 确保速度在有效范围内
        const normalizedSpeed = Math.max(0, Math.min(speed, maxSpeed));

        // 线性插值：0为红色，maxSpeed为绿色
        const ratio = normalizedSpeed / maxSpeed;

        // RGB插值：红色(255,0,0) 到 绿色(0,255,0)
        const red = Math.round(255 * (1 - ratio));
        const green = Math.round(255 * ratio);
        const blue = 0;

        return `rgb(${red}, ${green}, ${blue})`;
    }

    // 创建回放模式的车辆图标
    function createReplayVehicleIcon(isVip = false, speed = 0) {
        // 车辆速度：10以上为绿色，0为红色
        const color = getSpeedColor(speed, 10);

        if (isVip) {
            // VIP车辆使用五角星
            const starSize = 20;
            const starSvg = `<svg width="${starSize}" height="${starSize}" viewBox="0 0 24 24">
                <polygon points="12,2 15.09,8.26 22,9.27 17,14.14 18.18,21.02 12,17.77 5.82,21.02 7,14.14 2,9.27 8.91,8.26"
                fill="${color}" stroke="#000" stroke-width="1" />
            </svg>`;

            return L.divIcon({
                html: starSvg,
                className: 'entity-icon',
                iconSize: [starSize, starSize],
                iconAnchor: [starSize/2, starSize/2]
            });
        } else {
            // 普通车辆使用方形
            const squareSize = 12;
            return L.divIcon({
                html: `<div style="background-color: ${color}; width: ${squareSize}px; height: ${squareSize}px; border: 1px solid #000;"></div>`,
                className: 'entity-icon',
                iconSize: [squareSize, squareSize],
                iconAnchor: [squareSize/2, squareSize/2]
            });
        }
    }

    // 创建回放模式的行人图标
    function createReplayPersonIcon(speed = 0) {
        // 行人速度：1以上为绿色，0为红色
        const color = getSpeedColor(speed, 1);
        const circleSize = 8;

        return L.divIcon({
            html: `<div style="background-color: ${color}; width: ${circleSize}px; height: ${circleSize}px; border-radius: 50%; border: 1px solid #000;"></div>`,
            className: 'entity-icon',
            iconSize: [circleSize, circleSize],
            iconAnchor: [circleSize/2, circleSize/2]
        });
    }

    // 清除所有回放实体
    function clearReplayEntities() {
        Object.values(replayEntityMarkers).forEach(marker => marker.remove());
        replayEntityMarkers = {};
    }
    
    // 验证配置
    function validateConfig(config) {
        // 基本检查
        if (!config) {
            showError('无法获取配置信息');
            return false;
        }
        
        // 检查路网配置
        if (!config.network_config) {
            showError('缺少路网配置');
            return false;
        }
        
        // 如果使用自定义路网，检查是否上传了文件
        if (config.network_config.type === 'custom' && !uploadedNetFile) {
            showError('请先上传自定义路网文件');
            return false;
        }
        
        // 如果使用自定义交通流，检查是否上传了文件
        if (config.traffic_config && config.traffic_config.type === 'custom' && !uploadedRouFile) {
            showError('请先上传自定义交通流文件');
            return false;
        }
        
        // 如果使用自定义信号配置，检查是否上传了文件
        if (config.signal_config && config.signal_config.type === 'custom' && !uploadedAddFile) {
            showError('请先上传自定义信号配置文件');
            return false;
        }
        
        // 如果启用了路段分析，检查是否选择了路段
        if (config.analysis_config && 
            config.analysis_config.edge_analysis && 
            config.analysis_config.edge_analysis.enabled && 
            (!config.analysis_config.edge_analysis.selected_edges || 
             config.analysis_config.edge_analysis.selected_edges.length === 0)) {
            showError('启用了路段分析，但未选择任何路段');
            return false;
        }
        
        return true;
    }

    // 显示加载中
    function showLoading(message) {
        // 检查是否已存在加载层
        let loadingOverlay = document.getElementById('loadingOverlay');
        if (!loadingOverlay) {
            loadingOverlay = document.createElement('div');
            loadingOverlay.id = 'loadingOverlay';
            loadingOverlay.innerHTML = `
                <div class="loading-spinner"></div>
                <div class="loading-message" id="loadingMessage"></div>
            `;

            document.body.appendChild(loadingOverlay);

            // 添加样式
            const style = document.createElement('style');
            style.textContent = `
                #loadingOverlay {
                    position: fixed;
                    top: 0;
                    left: 0;
                    width: 100%;
                    height: 100%;
                    background-color: rgba(0, 0, 0, 0.7);
                    display: flex;
                    flex-direction: column;
                    justify-content: center;
                    align-items: center;
                    z-index: 9999;
                }
                .loading-spinner {
                    border: 5px solid #f3f3f3;
                    border-top: 5px solid #3498db;
                    border-radius: 50%;
                    width: 50px;
                    height: 50px;
                    animation: spin 2s linear infinite;
                }
                .loading-message {
                    color: white;
                    margin-top: 20px;
                    font-size: 18px;
                }
                @keyframes spin {
                    0% { transform: rotate(0deg); }
                    100% { transform: rotate(360deg); }
                }
            `;
            document.head.appendChild(style);
        }

        // 更新加载消息
        document.getElementById('loadingMessage').textContent = message;
        loadingOverlay.style.display = 'flex';
    }

    // 隐藏加载中
    function hideLoading() {
        const loadingOverlay = document.getElementById('loadingOverlay');
        if (loadingOverlay) {
            loadingOverlay.style.display = 'none';
        }
    }

    // 显示错误信息
    function showError(message) {
        alert(`错误: ${message}`);
    }

    // 显示成功提示
    function showSuccess(message) {
        alert(`成功: ${message}`);
    }

    // 主体 updateDisplay
function updateDisplay(data) {
    console.log('更新显示结果 ID:', data.simulation_id);

    // 保存到全局
    currentDisplayedData = JSON.parse(JSON.stringify(data));
    window.currentDisplayedData = JSON.parse(JSON.stringify(data));

    // 仅在非对比模式下更新缓存
    const isComparingMode = document.querySelector('.comparison-header') !== null;
    if (!isComparingMode) {
        currentLoadedResultData = JSON.parse(JSON.stringify(data));
        window.currentLoadedResultData = JSON.parse(JSON.stringify(data));
    }
    window.resultDataLoaded = true;

    // 仿真信息
    document.getElementById('simId').textContent = data.simulation_id;
    const startTimeRaw = new Date(data.start_time);
    const formattedTime = startTimeRaw.toLocaleString('zh-CN', {
        year: 'numeric', month: '2-digit', day: '2-digit',
        hour: '2-digit', minute: '2-digit', second: '2-digit'
    }).replace(/\//g, '-');
    document.getElementById('startTime').textContent = formattedTime;

    // 配置摘要
    if (data.config_summary) {
        const networkElement = document.querySelector('#networkConfig span');
        const signalElement = document.querySelector('#signalConfig span');
        const trafficElement = document.querySelector('#trafficConfig span');
        if (networkElement) networkElement.textContent = data.config_summary.network || '未知';
        if (signalElement) signalElement.textContent = data.config_summary.signal || '未知';
        if (trafficElement) trafficElement.textContent = data.config_summary.traffic || '未知';
    }

    // 获取并整理各类指标
    const results = data.simulation_results;
    let networkMetrics;
    if (results.network_metrics) {
        networkMetrics = results.network_metrics;
    } else {
        networkMetrics = {
            pedestrian_metrics: results.pedestrian_metrics || {},
            vehicle_metrics: results.vehicle_metrics || {},
            vip_vehicle_metrics: results.vip_vehicle_metrics || {},
            venue_area_metrics: results.venue_area_metrics || {}
        };
    }
    const allMetrics = {
        pedestrian_metrics: networkMetrics.pedestrian_metrics || {},
        vehicle_metrics: networkMetrics.vehicle_metrics || {},
        vip_vehicle_metrics: networkMetrics.vip_vehicle_metrics || {},
        venue_area_metrics: networkMetrics.venue_area_metrics || {}
    };
    ensureAllMetricsDefaults(allMetrics);

    // 用自己的4个chart函数渲染
    console.log("即将创建四个主chart", allMetrics);
    createTravelTimeChart(allMetrics);
    createWaitingTimeChart(allMetrics);
    createWaitingCountChart(allMetrics);
    createTimeLossDelayChart(allMetrics);

    // 路段分析
    if (results.selected_edge_metrics) {
        displayEdgeAnalysisResults(results.selected_edge_metrics);
    }

    // 根据配置渲染路网示意图（异步）
    // 注释掉以避免与回放地图冲突
    // if (data.config) {
    //     renderNetworkOverview(data.config);
    // }
    console.log('跳过路网示意图渲染以保护回放地图');
}

    // 确保指标有默认值
    function ensureMetricsDefaults(metrics) {
        const defaults = {
            average_travel_time: 0,
            average_waiting_time: 0,
            average_waiting_count: 0,
            average_time_loss: 0,
            average_pedestrian_travel_time: 0,
            average_pedestrian_delay: 0,
            average_vehicle_travel_time: 0,
            average_vehicle_delay: 0
        };

        return { ...defaults, ...metrics };
    }

    // 安全创建图表
    function safeCreateChart(chartFunctionName, metrics) {
        try {
            console.log(`正在创建/更新图表: ${chartFunctionName}`, metrics);
            // 确保指标有默认值
            const safeMetrics = ensureMetricsDefaults(metrics || {});

            // 检查图表创建函数是否存在
            if (typeof window[chartFunctionName] === 'function') {
                // 直接调用图表创建函数
                window[chartFunctionName](safeMetrics);
                console.log(`${chartFunctionName} 调用成功`);
            } else {
                console.warn(`图表创建函数 ${chartFunctionName} 不存在，尝试替代方案`);
                
                // 根据函数名判断图表类型并使用直接方法
                if (chartFunctionName === 'createPedestrianChart') {
                    updatePedestrianChart(safeMetrics);
                } else if (chartFunctionName === 'createVehicleChart') {
                    updateVehicleChart(safeMetrics);
                } else if (chartFunctionName === 'createVIPVehicleChart') {
                    updateVIPVehicleChart(safeMetrics);
                } else if (chartFunctionName === 'createVenueChart') {
                    updateVenueChart(safeMetrics);
                }
            }
        } catch (error) {
            console.error(`创建图表时出错 (${chartFunctionName}):`, error);
        }
    }
    
    // 辅助函数：直接更新行人图表
    function updatePedestrianChart(metrics) {
        const ctx = document.getElementById('pedChart');
        if (!ctx) {
            console.warn('未找到pedChart画布元素');
            return;
        }
        
        // 销毁已存在的图表实例
        if (window.pedestrianChart instanceof Chart) {
            window.pedestrianChart.destroy();
        }

        window.pedestrianChart = new Chart(ctx.getContext('2d'), {
            type: 'bar',
            data: {
                labels: ['平均行程时间', '平均等待时间', '平均等待次数', '平均时间损失'],
                datasets: [{
                    label: '行人指标',
                    data: [
                        metrics.average_travel_time,
                        metrics.average_waiting_time,
                        metrics.average_waiting_count,
                        metrics.average_time_loss
                    ],
                    backgroundColor: [
                        'rgba(255, 99, 132, 0.8)',
                        'rgba(54, 162, 235, 0.8)',
                        'rgba(255, 206, 86, 0.8)',
                        'rgba(75, 192, 192, 0.8)'
                    ],
                    borderColor: [
                        'rgba(255, 99, 132, 1)',
                        'rgba(54, 162, 235, 1)',
                        'rgba(255, 206, 86, 1)',
                        'rgba(75, 192, 192, 1)'
                    ],
                    borderWidth: 1
                }]
            },
            options: {
                responsive: true,
                maintainAspectRatio: false,
                scales: {
                    y: {
                        beginAtZero: true,
                        grid: { color: 'rgba(255, 255, 255, 0.1)' },
                        ticks: { color: '#ffffff' }
                    },
                    x: {
                        grid: { color: 'rgba(255, 255, 255, 0.1)' },
                        ticks: { color: '#ffffff' }
                    }
                },
                plugins: {
                    legend: {
                        display: true,
                        labels: { color: '#ffffff' }
                    },
                    title: {
                        display: true,
                        text: '行人指标',
                        color: '#ffffff'
                    }
                }
            }
        });
    }
    
    // 辅助函数：直接更新车辆图表
    function updateVehicleChart(metrics) {
        const ctx = document.getElementById('vehChart');
        if (!ctx) {
            console.warn('未找到vehChart画布元素');
            return;
        }
        
        // 销毁已存在的图表实例
        if (window.vehicleChart instanceof Chart) {
            window.vehicleChart.destroy();
        }

        window.vehicleChart = new Chart(ctx.getContext('2d'), {
            type: 'bar',
            data: {
                labels: ['平均行程时间', '平均等待时间', '平均等待次数', '平均时间损失'],
                datasets: [{
                    label: '一般车辆指标',
                    data: [
                        metrics.average_travel_time,
                        metrics.average_waiting_time,
                        metrics.average_waiting_count,
                        metrics.average_time_loss
                    ],
                    backgroundColor: [
                        'rgba(255, 99, 132, 0.8)',
                        'rgba(54, 162, 235, 0.8)',
                        'rgba(255, 206, 86, 0.8)',
                        'rgba(75, 192, 192, 0.8)'
                    ],
                    borderColor: [
                        'rgba(255, 99, 132, 1)',
                        'rgba(54, 162, 235, 1)',
                        'rgba(255, 206, 86, 1)',
                        'rgba(75, 192, 192, 1)'
                    ],
                    borderWidth: 1
                }]
            },
            options: {
                responsive: true,
                maintainAspectRatio: false,
                scales: {
                    y: {
                        beginAtZero: true,
                        grid: { color: 'rgba(255, 255, 255, 0.1)' },
                        ticks: { color: '#ffffff' }
                    },
                    x: {
                        grid: { color: 'rgba(255, 255, 255, 0.1)' },
                        ticks: { color: '#ffffff' }
                    }
                },
                plugins: {
                    legend: {
                        display: true,
                        labels: { color: '#ffffff' }
                    },
                    title: {
                        display: true,
                        text: '一般车辆指标',
                        color: '#ffffff'
                    }
                }
            }
        });
    }
    
    // 辅助函数：直接更新贵宾车辆图表
    function updateVIPVehicleChart(metrics) {
        const ctx = document.getElementById('vipChart');
        if (!ctx) {
            console.warn('未找到vipChart画布元素');
            return;
        }
        
        // 销毁已存在的图表实例
        if (window.vipVehicleChart instanceof Chart) {
            window.vipVehicleChart.destroy();
        }

        window.vipVehicleChart = new Chart(ctx.getContext('2d'), {
            type: 'bar',
            data: {
                labels: ['平均行程时间', '平均等待时间', '平均等待次数', '平均时间损失'],
                datasets: [{
                    label: '贵宾专车指标',
                    data: [
                        metrics.average_travel_time,
                        metrics.average_waiting_time,
                        metrics.average_waiting_count,
                        metrics.average_time_loss
                    ],
                    backgroundColor: [
                        'rgba(255, 99, 132, 0.8)',
                        'rgba(54, 162, 235, 0.8)',
                        'rgba(255, 206, 86, 0.8)',
                        'rgba(75, 192, 192, 0.8)'
                    ],
                    borderColor: [
                        'rgba(255, 99, 132, 1)',
                        'rgba(54, 162, 235, 1)',
                        'rgba(255, 206, 86, 1)',
                        'rgba(75, 192, 192, 1)'
                    ],
                    borderWidth: 1
                }]
            },
            options: {
                responsive: true,
                maintainAspectRatio: false,
                scales: {
                    y: {
                        beginAtZero: true,
                        grid: { color: 'rgba(255, 255, 255, 0.1)' },
                        ticks: { color: '#ffffff' }
                    },
                    x: {
                        grid: { color: 'rgba(255, 255, 255, 0.1)' },
                        ticks: { color: '#ffffff' }
                    }
                },
                plugins: {
                    legend: {
                        display: true,
                        labels: { color: '#ffffff' }
                    },
                    title: {
                        display: true,
                        text: '贵宾专车指标',
                        color: '#ffffff'
                    }
                }
            }
        });
    }
    
    // 辅助函数：直接更新场馆区域图表
    function updateVenueChart(metrics) {
        const ctx = document.getElementById('venueChart');
        if (!ctx) {
            console.warn('未找到venueChart画布元素');
            return;
        }
        
        // 销毁已存在的图表实例
        if (window.venueChart instanceof Chart) {
            window.venueChart.destroy();
        }

        window.venueChart = new Chart(ctx.getContext('2d'), {
            type: 'bar',
            data: {
                labels: ['行人平均行程时间', '行人平均延误', '车辆平均行程时间', '车辆平均延误'],
                datasets: [{
                    label: '场馆区域指标',
                    data: [
                        metrics.average_pedestrian_travel_time,
                        metrics.average_pedestrian_delay,
                        metrics.average_vehicle_travel_time,
                        metrics.average_vehicle_delay
                    ],
                    backgroundColor: [
                        'rgba(255, 99, 132, 0.8)',
                        'rgba(54, 162, 235, 0.8)',
                        'rgba(255, 206, 86, 0.8)',
                        'rgba(75, 192, 192, 0.8)'
                    ],
                    borderColor: [
                        'rgba(255, 99, 132, 1)',
                        'rgba(54, 162, 235, 1)',
                        'rgba(255, 206, 86, 1)',
                        'rgba(75, 192, 192, 1)'
                    ],
                    borderWidth: 1
                }]
            },
            options: {
                responsive: true,
                maintainAspectRatio: false,
                scales: {
                    y: {
                        beginAtZero: true,
                        grid: { color: 'rgba(255, 255, 255, 0.1)' },
                        ticks: { color: '#ffffff' }
                    },
                    x: {
                        grid: { color: 'rgba(255, 255, 255, 0.1)' },
                        ticks: { color: '#ffffff' }
                    }
                },
                plugins: {
                    legend: {
                        display: true,
                        labels: { color: '#ffffff' }
                    },
                    title: {
                        display: true,
                        text: '场馆区域指标',
                        color: '#ffffff'
                    }
                }
            }
        });
    }
// 渲染"平均行程时间对比"图表
function renderTravelTimeChart(resultData) {
    const ctx = document.getElementById('travelTimeChart').getContext('2d');
    if (window.travelTimeChartInstance) window.travelTimeChartInstance.destroy();

    window.travelTimeChartInstance = new Chart(ctx, {
        type: 'bar',
        data: {
            labels: ['行人', '一般车辆', '贵宾车辆', '场馆车辆'],
            datasets: [{
                label: '平均行程时间 (s)',
                data: [
                    resultData.network_metrics?.pedestrian_metrics?.average_travel_time || 0,
                    resultData.network_metrics?.vehicle_metrics?.average_travel_time || 0,
                    resultData.network_metrics?.vip_vehicle_metrics?.average_travel_time || 0,
                    resultData.network_metrics?.venue_area_metrics?.average_vehicle_travel_time || 0,
                ],
                backgroundColor: [
                    'rgba(255, 99, 132, 0.8)',
                    'rgba(54, 162, 235, 0.8)',
                    'rgba(255, 206, 86, 0.8)',
                    'rgba(75, 192, 192, 0.8)'
                ]
            }]
        },
        options: { responsive: true, plugins: { legend: { display: false } } }
    });
}

// 渲染"平均等待时间对比"图表
function renderWaitingTimeChart(resultData) {
    const ctx = document.getElementById('waitingTimeChart').getContext('2d');
    if (window.waitingTimeChartInstance) window.waitingTimeChartInstance.destroy();

    window.waitingTimeChartInstance = new Chart(ctx, {
        type: 'bar',
        data: {
            labels: ['行人', '一般车辆', '贵宾车辆'],
            datasets: [{
                label: '平均等待时间 (s)',
                data: [
                    resultData.network_metrics?.pedestrian_metrics?.average_waiting_time || 0,
                    resultData.network_metrics?.vehicle_metrics?.average_waiting_time || 0,
                    resultData.network_metrics?.vip_vehicle_metrics?.average_waiting_time || 0,
                ],
                backgroundColor: [
                    'rgba(255, 99, 132, 0.8)',
                    'rgba(54, 162, 235, 0.8)',
                    'rgba(255, 206, 86, 0.8)'
                ]
            }]
        },
        options: { responsive: true, plugins: { legend: { display: false } } }
    });
}

// 渲染"平均等待次数对比"图表
function renderWaitingCountChart(resultData) {
    const ctx = document.getElementById('waitingCountChart').getContext('2d');
    if (window.waitingCountChartInstance) window.waitingCountChartInstance.destroy();

    window.waitingCountChartInstance = new Chart(ctx, {
        type: 'bar',
        data: {
            labels: ['行人', '一般车辆', '贵宾车辆'],
            datasets: [{
                label: '平均等待次数',
                data: [
                    resultData.network_metrics?.pedestrian_metrics?.average_waiting_count || 0,
                    resultData.network_metrics?.vehicle_metrics?.average_waiting_count || 0,
                    resultData.network_metrics?.vip_vehicle_metrics?.average_waiting_count || 0,
                ],
                backgroundColor: [
                    'rgba(255, 99, 132, 0.8)',
                    'rgba(54, 162, 235, 0.8)',
                    'rgba(255, 206, 86, 0.8)'
                ]
            }]
        },
        options: { responsive: true, plugins: { legend: { display: false } } }
    });
}

// 渲染"时间损失与延误对比"图表
function renderTimeLossDelayChart(resultData) {
    const ctx = document.getElementById('timeLossDelayChart').getContext('2d');
    if (window.timeLossDelayChartInstance) window.timeLossDelayChartInstance.destroy();

    window.timeLossDelayChartInstance = new Chart(ctx, {
        type: 'bar',
        data: {
            labels: ['行人-时间损失', '一般车辆-时间损失', '贵宾车辆-时间损失', '场馆车辆-延误', '场馆行人-延误'],
            datasets: [{
                label: '时间损失/延误 (s)',
                data: [
                    resultData.network_metrics?.pedestrian_metrics?.average_time_loss || 0,
                    resultData.network_metrics?.vehicle_metrics?.average_time_loss || 0,
                    resultData.network_metrics?.vip_vehicle_metrics?.average_time_loss || 0,
                    resultData.network_metrics?.venue_area_metrics?.average_vehicle_delay || 0,
                    resultData.network_metrics?.venue_area_metrics?.average_pedestrian_delay || 0,
                ],
                backgroundColor: [
                    'rgba(255, 99, 132, 0.8)',
                    'rgba(54, 162, 235, 0.8)',
                    'rgba(255, 206, 86, 0.8)',
                    'rgba(75, 192, 192, 0.8)',
                    'rgba(153, 102, 255, 0.8)'
                ]
            }]
        },
        options: { responsive: true, plugins: { legend: { display: false } } }
    });
}
function compareChartDisplay(oldResult, newResult) {
    // 辅助：统一格式化
    function getMetrics(res) {
        const results = res.simulation_results;
        let networkMetrics;
        if (results.network_metrics) {
            networkMetrics = results.network_metrics;
        } else {
            networkMetrics = {
                pedestrian_metrics: results.pedestrian_metrics || {},
                vehicle_metrics: results.vehicle_metrics || {},
                vip_vehicle_metrics: results.vip_vehicle_metrics || {},
                venue_area_metrics: results.venue_area_metrics || {}
            };
        }
        return {
            pedestrian_metrics: networkMetrics.pedestrian_metrics || {},
            vehicle_metrics: networkMetrics.vehicle_metrics || {},
            vip_vehicle_metrics: networkMetrics.vip_vehicle_metrics || {},
            venue_area_metrics: networkMetrics.venue_area_metrics || {}
        };
    }

    const oldMetrics = getMetrics(oldResult);
    const newMetrics = getMetrics(newResult);

    // 下面四个函数你已有，粘贴时可以直接覆盖或另写四个"对比版"
    createComparisonTravelTimeChart(oldMetrics, newMetrics, newResult.simulation_id);
    createComparisonWaitingTimeChart(oldMetrics, newMetrics, newResult.simulation_id);
    createComparisonWaitingCountChart(oldMetrics, newMetrics, newResult.simulation_id);
    createComparisonTimeLossDelayChart(oldMetrics, newMetrics, newResult.simulation_id);
}
// 总入口，每次加载方案后调用一次即可
function updateMultiComparisonCharts(resultsArr) {
    createMultiComparisonTravelTimeChart(resultsArr);
    createMultiComparisonWaitingTimeChart(resultsArr);
    createMultiComparisonWaitingCountChart(resultsArr);
    createMultiComparisonTimeLossDelayChart(resultsArr);
}

// 1. 平均行程时间对比
function createMultiComparisonTravelTimeChart(resultsArr) {
    const ctx = document.getElementById('travelTimeChart').getContext('2d');
    if (window.travelTimeChart instanceof Chart) window.travelTimeChart.destroy();

    const labels = ['行人', '一般车辆', '贵宾专车', '场馆区域行人', '场馆区域车辆'];
    const datasets = resultsArr.map((res, idx) => {
        const m = res.simulation_results.network_metrics || {};
        const ped = m.pedestrian_metrics || {};
        const veh = m.vehicle_metrics || {};
        const vip = m.vip_vehicle_metrics || {};
        const venue = m.venue_area_metrics || {};
        return {
            label: `${res.name || res.id}`,
            data: [
                ped.average_travel_time,
                veh.average_travel_time,
                vip.average_travel_time,
                venue.average_pedestrian_travel_time,
                venue.average_vehicle_travel_time
            ],
            backgroundColor: `rgba(${100+idx*40},${120+idx*30},${180-idx*30},0.7)`,
            borderColor: `rgba(${100+idx*40},${120+idx*30},${180-idx*30},1)`,
            borderWidth: 1
        }
    });

    window.travelTimeChart = new Chart(ctx, {
        type: 'bar',
        data: { labels, datasets },
        options: {
            responsive: true,
            maintainAspectRatio: false,
            plugins: { legend: { labels: { color: '#fff' } }, title: { display: true, text: '平均行程时间对比', color: '#fff' } },
            scales: { x: { ticks: { color: '#fff' } }, y: { beginAtZero: true, ticks: { color: '#fff' } } }
        }
    });
}

// 2. 平均等待时间对比
function createMultiComparisonWaitingTimeChart(resultsArr) {
    const ctx = document.getElementById('waitingTimeChart').getContext('2d');
    if (window.waitingTimeChart instanceof Chart) window.waitingTimeChart.destroy();

    const labels = ['行人', '一般车辆', '贵宾专车'];
    const datasets = resultsArr.map((res, idx) => {
        const m = res.simulation_results.network_metrics || {};
        const ped = m.pedestrian_metrics || {};
        const veh = m.vehicle_metrics || {};
        const vip = m.vip_vehicle_metrics || {};
        return {
            label: `${res.name || res.id}`,
            data: [
                ped.average_waiting_time,
                veh.average_waiting_time,
                vip.average_waiting_time
            ],
            backgroundColor: `rgba(${130+idx*40},${70+idx*50},${200-idx*20},0.7)`,
            borderColor: `rgba(${130+idx*40},${70+idx*50},${200-idx*20},1)`,
            borderWidth: 1
        }
    });

    window.waitingTimeChart = new Chart(ctx, {
        type: 'bar',
        data: { labels, datasets },
        options: {
            responsive: true,
            maintainAspectRatio: false,
            plugins: { legend: { labels: { color: '#fff' } }, title: { display: true, text: '平均等待时间对比', color: '#fff' } },
            scales: { x: { ticks: { color: '#fff' } }, y: { beginAtZero: true, ticks: { color: '#fff' } } }
        }
    });
}

// 3. 平均等待次数对比
function createMultiComparisonWaitingCountChart(resultsArr) {
    const ctx = document.getElementById('waitingCountChart').getContext('2d');
    if (window.waitingCountChart instanceof Chart) window.waitingCountChart.destroy();

    const labels = ['行人', '一般车辆', '贵宾专车'];
    const datasets = resultsArr.map((res, idx) => {
        const m = res.simulation_results.network_metrics || {};
        const ped = m.pedestrian_metrics || {};
        const veh = m.vehicle_metrics || {};
        const vip = m.vip_vehicle_metrics || {};
        return {
            label: `${res.name || res.id}`,
            data: [
                ped.average_waiting_count,
                veh.average_waiting_count,
                vip.average_waiting_count
            ],
            backgroundColor: `rgba(${180-idx*30},${50+idx*60},${160+idx*30},0.7)`,
            borderColor: `rgba(${180-idx*30},${50+idx*60},${160+idx*30},1)`,
            borderWidth: 1
        }
    });

    window.waitingCountChart = new Chart(ctx, {
        type: 'bar',
        data: { labels, datasets },
        options: {
            responsive: true,
            maintainAspectRatio: false,
            plugins: { legend: { labels: { color: '#fff' } }, title: { display: true, text: '平均等待次数对比', color: '#fff' } },
            scales: { x: { ticks: { color: '#fff' } }, y: { beginAtZero: true, ticks: { color: '#fff' } } }
        }
    });
}

// 4. 时间损失与延误对比
function createMultiComparisonTimeLossDelayChart(resultsArr) {
    const ctx = document.getElementById('timeLossDelayChart').getContext('2d');
    if (window.timeLossDelayChart instanceof Chart) window.timeLossDelayChart.destroy();

    const labels = ['行人时间损失', '一般车辆时间损失', '贵宾专车时间损失', '场馆区域行人延误', '场馆区域车辆延误'];
    const datasets = resultsArr.map((res, idx) => {
        const m = res.simulation_results.network_metrics || {};
        const ped = m.pedestrian_metrics || {};
        const veh = m.vehicle_metrics || {};
        const vip = m.vip_vehicle_metrics || {};
        const venue = m.venue_area_metrics || {};
        return {
            label: `${res.name || res.id}`,
            data: [
                ped.average_time_loss,
                veh.average_time_loss,
                vip.average_time_loss,
                venue.average_pedestrian_delay,
                venue.average_vehicle_delay
            ],
            backgroundColor: `rgba(${60+idx*50},${100+idx*30},${120+idx*30},0.7)`,
            borderColor: `rgba(${60+idx*50},${100+idx*30},${120+idx*30},1)`,
            borderWidth: 1
        }
    });

    window.timeLossDelayChart = new Chart(ctx, {
        type: 'bar',
        data: { labels, datasets },
        options: {
            responsive: true,
            maintainAspectRatio: false,
            plugins: { legend: { labels: { color: '#fff' } }, title: { display: true, text: '时间损失与延误对比', color: '#fff' } },
            scales: { x: { ticks: { color: '#fff' } }, y: { beginAtZero: true, ticks: { color: '#fff' } } }
        }
    });
}




    // 显示路段分析结果
    function displayEdgeAnalysisResults(edgeMetrics) {
        const edgeSection = document.getElementById('edgeAnalysisSection');
        if (edgeSection) {
            edgeSection.style.display = 'block';

            // 安全更新路段指标的辅助函数
            function safeUpdateEdgeMetric(elementId, value, defaultValue = '-', isPercentage = false, decimals = 2) {
                const element = document.getElementById(elementId);
                if (element) {
                    if (value !== undefined && value !== null && !isNaN(value)) {
                        if (isPercentage) {
                            element.textContent = (Number(value) * 100).toFixed(1) + '%';
                        } else {
                            element.textContent = Number(value).toFixed(decimals);
                        }
                    } else {
                        element.textContent = defaultValue;
                    }
                }
            }

            // 更新路段行人指标
            const edgePedMetrics = edgeMetrics.pedestrian_metrics || {};
            safeUpdateEdgeMetric('edgePedTravelTime', edgePedMetrics.avg_traveltime);
            safeUpdateEdgeMetric('edgePedWaitingTime', edgePedMetrics.avg_waitingTime);
            safeUpdateEdgeMetric('edgePedSpeed', edgePedMetrics.avg_speed);
            safeUpdateEdgeMetric('edgePedTimeLoss', edgePedMetrics.avg_timeLoss);
            safeUpdateEdgeMetric('edgePedCount', edgePedMetrics.total_entered, '-', false, 0);
            safeUpdateEdgeMetric('edgePedOccupancy', edgePedMetrics.avg_occupancy, '-', true);

            // 更新路段车辆指标
            const edgeVehMetrics = edgeMetrics.vehicle_metrics || {};
            safeUpdateEdgeMetric('edgeVehTravelTime', edgeVehMetrics.avg_traveltime);
            safeUpdateEdgeMetric('edgeVehWaitingTime', edgeVehMetrics.avg_waitingTime);
            safeUpdateEdgeMetric('edgeVehSpeed', edgeVehMetrics.avg_speed);
            safeUpdateEdgeMetric('edgeVehTimeLoss', edgeVehMetrics.avg_timeLoss);
            safeUpdateEdgeMetric('edgeVehCount', edgeVehMetrics.total_entered, '-', false, 0);
            safeUpdateEdgeMetric('edgeVehOccupancy', edgeVehMetrics.avg_occupancy, '-', true);
        } else {
            console.warn('未找到路段分析结果显示区域');
        }
    }

    // 通知提示
    function showNotification(message, type = 'info') {
        // 检查是否已存在通知容器
        let container = document.querySelector('.notification-container');
        if (!container) {
            container = document.createElement('div');
            container.className = 'notification-container';
            document.body.appendChild(container);

            // 添加通知容器样式
            const style = document.createElement('style');
            style.textContent = `
                .notification-container {
                    position: fixed;
                    top: 20px;
                    right: 20px;
                    z-index: 1000;
                }
                .notification {
                    padding: 12px 20px;
                    margin-bottom: 10px;
                    border-radius: 4px;
                    color: white;
                    box-shadow: 0 3px 6px rgba(0,0,0,0.16);
                    animation: slide-in 0.3s ease-out forwards;
                    max-width: 300px;
                }
                .notification.info {
                    background-color: var(--primary-color);
                }
                .notification.success {
                    background-color: var(--secondary-color);
                }
                .notification.error {
                    background-color: #ea4335;
                }
                @keyframes slide-in {
                    0% { transform: translateX(100%); opacity: 0; }
                    100% { transform: translateX(0); opacity: 1; }
                }
                @keyframes fade-out {
                    0% { transform: translateX(0); opacity: 1; }
                    100% { transform: translateX(100%); opacity: 0; }
                }
                .notification.fade-out {
                    animation: fade-out 0.3s ease-in forwards;
                }
            `;
            document.head.appendChild(style);
        }

        // 创建通知元素
        const notification = document.createElement('div');
        notification.className = `notification ${type}`;
        notification.textContent = message;
        container.appendChild(notification);

        // 3秒后移除通知
        setTimeout(() => {
            notification.classList.add('fade-out');
            setTimeout(() => {
                container.removeChild(notification);
            }, 300);
        }, 3000);
    }
    // 更新对比方案配置摘要列表
function updateComparisonSchemesSummaryList() {
    const listDiv = document.getElementById('comparisonSchemesSummaryList');
    if (!listDiv) return;
    const compared = window.comparedResults || [];
    if (compared.length === 0) {
        listDiv.innerHTML = '<span style="color:#aaa;">暂无对比方案摘要</span>';
    } else {
        let html = '<ul style="padding-left: 16px;">';
        compared.forEach((item, idx) => {
            html += `
                <li style="margin-bottom:7px;">
                    <strong style="color:#f5d553;">${item.name ? item.name : '方案'+(idx+1)}</strong>
                    <span style="color:#7ebaf6;">（ID: ${item.id}）</span><br>
                    <span style="color:#aaffaa;">路网:</span> ${item.config_summary?.network || '未知'}<br>
                    <span style="color:#ffbb88;">信号:</span> ${item.config_summary?.signal || '未知'}<br>
                    <span style="color:#9be6ff;">交通:</span> ${item.config_summary?.traffic || '未知'}
                </li>
            `;
        });
        html += '</ul>';
        listDiv.innerHTML = html;
    }
}


    // 默认配置（备用）
    function getDefaultConfig() {
        return {
            "network_config": {
                "type": "predefined",
                "file_path": null,
                "entrance_plan": "仅开放东侧出入口",
                "road_restriction": {
                    "enabled": false,
                    "restricted_edges": []
                }
            },
            "signal_config": {
                "type": "predefined",
                "file_path": null,
                "optimization": {
                    "enabled": false,
                    "selected_intersections": []
                }
            },
            "traffic_config": {
                "type": "predefined",
                "file_path": null,
                "scenario": "进场",
                "vehicle_type": "仅一般车辆",
                "vip_priority": {
                    "enabled": false
                }
            },
            "analysis_config": {
                "edge_analysis": {
                    "enabled": false,
                    "selected_edges": []
                }
            }
        };
    }

    // ==================== 历史方案管理功能 ====================

    // 历史方案相关的DOM元素
    const historyModal = document.getElementById('historyModal');
    const saveSchemeModal = document.getElementById('saveSchemeModal');
    const closeHistoryModal = document.getElementById('closeHistoryModal');
    const closeSaveModal = document.getElementById('closeSaveModal');
    const saveCurrentBtn = document.getElementById('saveCurrentBtn');
    const refreshHistoryBtn = document.getElementById('refreshHistoryBtn');
    const historyList = document.getElementById('historyList');
    const confirmSaveBtn = document.getElementById('confirmSaveBtn');
    const cancelSaveBtn = document.getElementById('cancelSaveBtn');
    const schemeName = document.getElementById('schemeName');
    const schemeDescription = document.getElementById('schemeDescription');
    const configPreview = document.getElementById('configPreview');

    // 历史方案管理类
    class HistoryManager {
        constructor() {
            this.apiBase = 'http://localhost:8888';
            this.schemes = [];
            this.initializeEventListeners();
        }

        initializeEventListeners() {
            // 历史方案按钮点击
            historyBtn.addEventListener('click', () => {
                this.showHistoryModal();
            });

            // 模态框关闭
            closeHistoryModal.addEventListener('click', () => {
                this.hideHistoryModal();
            });

            closeSaveModal.addEventListener('click', () => {
                this.hideSaveModal();
            });

            // 点击模态框外部关闭
            window.addEventListener('click', (event) => {
                if (event.target === historyModal) {
                    this.hideHistoryModal();
                }
                if (event.target === saveSchemeModal) {
                    this.hideSaveModal();
                }
            });

            // 保存当前配置按钮
            saveCurrentBtn.addEventListener('click', () => {
                this.showSaveModal();
            });

            // 刷新历史列表按钮
            refreshHistoryBtn.addEventListener('click', () => {
                this.loadHistoryList();
            });

            // 确认保存按钮
            confirmSaveBtn.addEventListener('click', () => {
                this.saveCurrentScheme();
            });

            // 取消保存按钮
            cancelSaveBtn.addEventListener('click', () => {
                this.hideSaveModal();
            });
        }

        showHistoryModal() {
            historyModal.style.display = 'block';
            this.loadHistoryList();
        }

        hideHistoryModal() {
            historyModal.style.display = 'none';
        }

        showSaveModal() {
            // 生成配置预览
            const config = window.buildConfigJSON ? window.buildConfigJSON() : getDefaultConfig();
            this.updateConfigPreview(config);

            // 清空表单
            schemeName.value = '';
            schemeDescription.value = '';

            saveSchemeModal.style.display = 'block';
        }

        hideSaveModal() {
            saveSchemeModal.style.display = 'none';
        }

        updateConfigPreview(config) {
            try {
                // 生成配置摘要
                const summary = this.generateConfigSummary(config);
                configPreview.innerHTML = `
                    <div><strong>路网配置:</strong> ${summary.network}</div>
                    <div><strong>信号配置:</strong> ${summary.signal}</div>
                    <div><strong>交通配置:</strong> ${summary.traffic}</div>
                `;
            } catch (error) {
                configPreview.innerHTML = '<div style="color: #dc3545;">配置预览生成失败</div>';
                console.error('配置预览生成失败:', error);
            }
        }

        generateConfigSummary(config) {
            // 网络配置摘要
            const networkConfig = config.network_config || {};
            const networkType = networkConfig.type === 'predefined' ? '预设路网' : '自定义路网';
            const entrancePlan = networkConfig.entrance_plan || '未知';
            const roadRestriction = networkConfig.road_restriction || {};
            let restrictionText = '';
            if (roadRestriction.enabled) {
                const restrictedCount = (roadRestriction.restricted_edges || []).length;
                restrictionText = ` + 道路限行(${restrictedCount}个路段)`;
            }
            const networkSummary = `${networkType} - ${entrancePlan}${restrictionText}`;

            // 信号配置摘要
            const signalConfig = config.signal_config || {};
            const signalType = signalConfig.type === 'predefined' ? '预设配时' : '自定义配时';
            const optimization = signalConfig.optimization || {};
            let optimizationText = '';
            if (optimization.enabled) {
                const intersectionCount = (optimization.selected_intersections || []).length;
                optimizationText = ` + 自定义优化(${intersectionCount}个交叉口)`;
            }
            const signalSummary = `${signalType}${optimizationText}`;

            // 交通配置摘要
            const trafficConfig = config.traffic_config || {};
            const trafficType = trafficConfig.type === 'predefined' ? '预设需求' : '自定义需求';
            const scenario = trafficConfig.scenario || '未知';
            const vehicleType = trafficConfig.vehicle_type || '未知';
            const vipPriority = trafficConfig.vip_priority || {};
            const vipText = vipPriority.enabled ? ' + 贵宾专车' : '';
            const trafficSummary = `${trafficType} - ${scenario}场景 + ${vehicleType}${vipText}`;

            return {
                network: networkSummary,
                signal: signalSummary,
                traffic: trafficSummary
            };
        }

        async loadHistoryList() {
            try {
                historyList.innerHTML = '<div class="loading-message">正在加载历史方案...</div>';

                const response = await fetch(`${this.apiBase}/api/history/list`);
                const result = await response.json();

                if (result.success) {
                    this.schemes = result.schemes;
                    this.renderHistoryList();
                } else {
                    historyList.innerHTML = `<div class="empty-message">加载失败: ${result.error}</div>`;
                }
            } catch (error) {
                console.error('加载历史方案失败:', error);
                historyList.innerHTML = '<div class="empty-message">加载历史方案时发生错误</div>';
            }
        }

        renderHistoryList() {
            if (this.schemes.length === 0) {
                historyList.innerHTML = '<div class="empty-message">暂无保存的历史方案</div>';
                return;
            }

            const html = this.schemes.map(scheme => {
                const createdTime = new Date(scheme.created_time).toLocaleString('zh-CN');
                const description = scheme.description || '无描述';

                return `
                    <div class="history-item" data-scheme-id="${scheme.id}">
                        <div class="history-item-header">
                            <div class="history-item-title">${scheme.name}</div>
                            <div class="history-item-time">${createdTime}</div>
                        </div>
                        <div class="history-item-description">${description}</div>
                        <div class="history-item-config">
                            <div>路网: ${scheme.config_summary.network}</div>
                            <div>信号: ${scheme.config_summary.signal}</div>
                            <div>交通: ${scheme.config_summary.traffic}</div>
                        </div>
                        <div class="history-item-actions">
                            <button class="load-scheme-btn" onclick="historyManager.loadScheme('${scheme.id}')">
                                <i class="bi bi-download"></i> 加载方案
                            </button>
                            <button class="delete-scheme-btn" onclick="historyManager.deleteScheme('${scheme.id}')">
                                <i class="bi bi-trash"></i> 删除
                            </button>
                        </div>
                    </div>
                `;
            }).join('');

            historyList.innerHTML = html;
        }

        async saveCurrentScheme() {
    try {
        const config = window.buildConfigJSON ? window.buildConfigJSON() : getDefaultConfig();
        const name = schemeName.value.trim();
        const description = schemeDescription.value.trim();

        // 关键：拿到仿真结果（没有就是 null）
        let simulation_results = null;
        if (window.currentLoadedResultData && window.currentLoadedResultData.simulation_results) {
            simulation_results = window.currentLoadedResultData.simulation_results;
        }

        confirmSaveBtn.textContent = '保存中...';
        confirmSaveBtn.disabled = true;

        const response = await fetch(`${this.apiBase}/api/history/save`, {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json'
            },
            body: JSON.stringify({
                config: config,
                name: name || null,
                description: description || null,
                simulation_results: simulation_results   // <------ 新增这一行
            })
        });

        const result = await response.json();

        if (result.success) {
            showSuccess(`方案保存成功: ${result.message}`);
            this.hideSaveModal();
            this.loadHistoryList(); // 刷新历史列表
        } else {
            showError(`保存失败: ${result.error}`);
        }
    } catch (error) {
        console.error('保存方案失败:', error);
        showError('保存方案时发生错误，请检查网络连接');
    } finally {
        confirmSaveBtn.textContent = '确认保存';
        confirmSaveBtn.disabled = false;
    }
}


       async loadScheme(schemeId) {
    try {
        showLoading('正在加载方案配置...');
        const response = await fetch(`${this.apiBase}/api/history/get/${schemeId}`);
        const result = await response.json();
        if (result.success) {
            const scheme = result.scheme;
            this.applySchemeToUI(scheme.config);

            // 初始化全局对比队列
            if (!window.comparedResults) window.comparedResults = [];
            // 新增到对比数组
            if (scheme.simulation_results) {
                window.comparedResults.push({
                    id: scheme.id || scheme.simulation_id || 'N/A',
                    name: scheme.name,
                    start_time: scheme.start_time || new Date().toISOString(),
                    config: scheme.config,
                    config_summary: scheme.config_summary,
                    simulation_results: scheme.simulation_results
                });
                // 刷新对比图和对比方案栏
                updateMultiComparisonCharts(window.comparedResults);
                updateComparisonSchemesInfo();
                updateComparisonSchemesSummaryList();
            } else {
                showNotification('该方案无仿真结果，无法显示图表', 'warning');
            }

            showSuccess(`方案 "${scheme.name}" 加载成功`);
            this.hideHistoryModal();
        } else {
            showError(`加载方案失败: ${result.error}`);
        }
    } catch (error) {
        console.error('加载方案失败:', error);
        showError('加载方案时发生错误，请检查网络连接');
    } finally {
        hideLoading();
    }
}









        applySchemeToUI(config) {
            try {
                // 应用网络配置
                const networkConfig = config.network_config || {};
                if (networkConfig.type === 'predefined') {
                    document.getElementById('net-preset').checked = true;
                } else {
                    document.getElementById('net-custom').checked = true;
                }

                // 应用出入口方案
                const entrancePlan = networkConfig.entrance_plan;
                // 根据出入口方案设置选中的路段
                selectedEntranceEdges = [];
                if (entrancePlan === '仅开放东侧出入口') {
                    selectedEntranceEdges = ['people_east_in', 'people_east_out'];
                } else if (entrancePlan === '仅开放南侧出入口') {
                    selectedEntranceEdges = ['people_south_in', 'people_south_out'];
                } else if (entrancePlan === '全部开放') {
                    selectedEntranceEdges = ['people_east_in', 'people_east_out', 'people_south_in', 'people_south_out'];
                }
                window.selectedEntranceEdges = selectedEntranceEdges;
                updateEntranceSelectionStatus(selectedEntranceEdges.length);

                // 应用道路限行
                const roadRestriction = networkConfig.road_restriction || {};
                document.getElementById('roadRestriction').checked = roadRestriction.enabled || false;
                selectedRestrictedEdges = roadRestriction.restricted_edges || [];
                window.selectedRestrictedEdges = selectedRestrictedEdges;

                // 应用信号配置
                const signalConfig = config.signal_config || {};
                if (signalConfig.type === 'predefined') {
                    document.getElementById('signal-preset').checked = true;
                } else {
                    document.getElementById('signal-custom').checked = true;
                }

                // 应用信控优化
                const optimization = signalConfig.optimization || {};
                document.getElementById('signalOptimization').checked = optimization.enabled || false;
                selectedIntersections = optimization.selected_intersections || [];
                window.selectedIntersections = selectedIntersections;

                // 应用交通配置
                const trafficConfig = config.traffic_config || {};
                if (trafficConfig.type === 'predefined') {
                    document.getElementById('traffic-preset').checked = true;
                } else {
                    document.getElementById('traffic-custom').checked = true;
                }

                // 应用车辆类型
                const vehicleType = trafficConfig.vehicle_type;
                const vehicleSelect = document.getElementById('vehicleType');
                if (vehicleType === '仅一般车辆') {
                    vehicleSelect.value = 'general';
                } else if (vehicleType === '存在贵宾专车') {
                    vehicleSelect.value = 'vip';
                }

                // 应用组织时段
                const scenario = trafficConfig.scenario;
                const timePhaseSelect = document.getElementById('timePhase');
                if (scenario === '进场') {
                    timePhaseSelect.value = 'entrance';
                } else if (scenario === '离场') {
                    timePhaseSelect.value = 'exit';
                }

                // 应用贵宾专车优先通行
                const vipPriority = trafficConfig.vip_priority || {};
                document.getElementById('vipPriority').checked = vipPriority.enabled || false;

                // 应用分析配置
                const analysisConfig = config.analysis_config || {};
                const edgeAnalysis = analysisConfig.edge_analysis || {};
                document.getElementById('edgeAnalysis').checked = edgeAnalysis.enabled || false;
                selectedAnalysisEdges = edgeAnalysis.selected_edges || [];
                window.selectedAnalysisEdges = selectedAnalysisEdges;

                // 显示/隐藏分析详情区域
                const edgeAnalysisDetails = document.getElementById('edgeAnalysisDetails');
                if (edgeAnalysis.enabled) {
                    edgeAnalysisDetails.style.display = 'block';
                    updateSelectionStatus('edgeAnalysis', selectedAnalysisEdges.length);
                } else {
                    edgeAnalysisDetails.style.display = 'none';
                }

                // 更新UI状态
                updateEntranceTypeState();
                updateVipPriorityState();

            } catch (error) {
                console.error('应用方案配置失败:', error);
                showError('应用方案配置时发生错误');
            }
        }

        async deleteScheme(schemeId) {
            if (!confirm('确定要删除这个方案吗？此操作不可撤销。')) {
                return;
            }

            try {
                const response = await fetch(`${this.apiBase}/api/history/delete/${schemeId}`, {
                    method: 'DELETE'
                });

                const result = await response.json();

                if (result.success) {
                    showSuccess('方案删除成功');
                    this.loadHistoryList(); // 刷新历史列表
                } else {
                    showError(`删除失败: ${result.error}`);
                }
            } catch (error) {
                console.error('删除方案失败:', error);
                showError('删除方案时发生错误，请检查网络连接');
            }
        }
    }

    // 创建历史方案管理器实例
    const historyManager = new HistoryManager();

    // 将历史方案管理器暴露到全局作用域，供HTML中的onclick使用
    window.historyManager = historyManager;

    // 获取或创建选择器容器 - 修改为使用固定容器
    function getOrCreateRightSelectorContainer(containerId) {
        // 不再在右侧面板创建容器，始终使用固定的嵌入式容器
        const fixedContainer = document.getElementById('embedded-selector-view');
        if (!fixedContainer) {
            console.error('未找到固定的选择器容器');
            return null;
        }
        
        // 显示选择器视图，隐藏默认地图
        fixedContainer.style.display = 'block';
        const defaultMapView = document.getElementById('default-map-view');
        if (defaultMapView) {
            defaultMapView.style.display = 'none';
        }
        
        // 清除之前的内容
        fixedContainer.innerHTML = '';
        
        return fixedContainer;
    }

    /**
     * 渲染仿真方案对应的路网示意图，并根据配置高亮不同元素
     * @param {Object} config 仿真配置 JSON
     */
    async function renderNetworkOverview(config) {
        try {
            if (!config || !config.network_config) {
                console.warn('配置中缺少 network_config，无法渲染路网示意图');
                return;
            }

            // 创建/获取承载容器
            const container = getOrCreateRightSelectorContainer('networkOverviewContainer');
            if (!container) return;

            // 生成基础 DOM
            container.style.marginTop = '10px';
            container.innerHTML = `
                <div class="result-header" style="margin-bottom:6px;">
                    <h2>路网示意图</h2>
                </div>
                <canvas id="networkOverviewCanvas" style="width:100%;height:400px; display:block;"></canvas>
            `;

            const canvas = container.querySelector('#networkOverviewCanvas');
            // 设置画布尺寸（根据容器自适应宽度，高度固定）
            canvas.width  = container.clientWidth;
            canvas.height = 400;

            // 确定路网文件路径
            let netFilePath = config.network_config.file_path;
            if (!netFilePath || netFilePath.trim() === '') {
                // 使用默认预设路网
                netFilePath = 'sumo_data/templates/gym_tls.net.xml';
            }

            // 解析路网 XML
            await window.networkSelector.parseNetworkFile(netFilePath);

            // 绑定新的 canvas 到 networkSelector
            window.networkSelector.canvas = canvas;
            window.networkSelector.ctx    = canvas.getContext('2d');

            // 重置交互相关属性，确保仅展示不允许选择
            window.networkSelector.allowEdgeSelection         = false;
            window.networkSelector.allowIntersectionSelection = true; // 为了能绘制交叉口

            // 清空选择与颜色
            window.networkSelector.selectedEdgeIds.clear();
            window.networkSelector.selectedIntersectionIds.clear();
            window.networkSelector.edges.forEach(e => { e.selected = false; e.color = null; });
            window.networkSelector.intersections.forEach(i => { i.selected = false; i.color = null; });

            // 1) 道路限行（橙色）
            const roadRestriction = (config.network_config.road_restriction || {});
            (roadRestriction.restricted_edges || []).forEach(id => {
                const edge = window.networkSelector.edges.find(e => e.id === id);
                if (edge) edge.color = '#FF8800';
            });

            // 2) 自选路段分析（黄色）
            const analysisEdges = ((config.analysis_config || {}).edge_analysis || {}).selected_edges || [];
            analysisEdges.forEach(id => {
                const edge = window.networkSelector.edges.find(e => e.id === id);
                if (edge) edge.color = '#FFD700';
            });

            // 3) 信控优化交叉口（青色）
            const optCfg = (config.signal_config || {}).optimization || {};
            let inters = optCfg.selected_intersections || [];
            if (inters && typeof inters === 'object' && !Array.isArray(inters)) {
                inters = inters.intersections || [];
            }
            inters.forEach(id => {
                const inter = window.networkSelector.intersections.find(i => i.id === id);
                if (inter) inter.color = '#00FFFF';
            });

            // 猜测可能的优化路段（若配置为混合选择，标为绿色）
            if (optCfg.selected_intersections && typeof optCfg.selected_intersections === 'object' && optCfg.selected_intersections.edges) {
                optCfg.selected_intersections.edges.forEach(id => {
                    const edge = window.networkSelector.edges.find(e => e.id === id);
                    if (edge) edge.color = '#00FF7F';
                });
            }

            // 自适应视图并绘制
            window.networkSelector.baseScale = 1;
            window.networkSelector.fitToCanvas();
            window.networkSelector.drawNetwork();

        } catch (err) {
            console.error('渲染路网示意图失败:', err);
        }
    }

    // 初始化固定容器中的默认地图
    function initializeDefaultMap() {
        try {
            // 检查是否正在进行仿真，如果是则不加载默认地图
            if (isReplaying || replayData) {
                console.log('仿真正在进行中，跳过默认地图加载');
                return;
            }

            // 获取当前路网文件路径
            const netFilePath = getCurrentNetFilePath();
            if (netFilePath) {
                // 确保默认地图视图可见
                const defaultMapView = document.getElementById('default-map-view');
                if (defaultMapView) {
                    defaultMapView.style.display = 'flex';
                }

                // 显示加载中
                const mapPlaceholder = document.querySelector('.map-placeholder');
                if (mapPlaceholder) {
                    mapPlaceholder.innerHTML = '<div class="loading-spinner"></div><p>正在加载地图...</p>';
                }

                // 异步加载地图
                setTimeout(async () => {
                    try {
                        // 再次检查是否开始了仿真
                        if (isReplaying || replayData) {
                            console.log('仿真已开始，取消默认地图加载');
                            return;
                        }

                        // 显示普通地图
                        await window.networkSelector.showNormalMap(netFilePath);
                    } catch (error) {
                        console.error('加载地图失败:', error);
                        // 恢复默认占位符
                        if (mapPlaceholder) {
                            mapPlaceholder.innerHTML = '<i class="bi bi-map"></i><p>路网地图</p>';
                        }
                    }
                }, 500); // 延迟加载，确保networkSelector已经初始化
            }
        } catch (error) {
            console.error('初始化默认地图失败:', error);
        }
    }

    // 将默认地图初始化函数暴露到全局作用域
    window.initializeDefaultMap = initializeDefaultMap;
});
