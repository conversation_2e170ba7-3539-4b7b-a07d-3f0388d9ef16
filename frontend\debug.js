// 前端调试和错误检查脚本
(function() {
    'use strict';
    
    // 调试配置
    const DEBUG_CONFIG = {
        logLevel: 'info', // 'error', 'warn', 'info', 'debug'
        checkInterval: 5000, // 5秒检查一次
        enableConsoleLog: true
    };
    
    // 日志函数
    function log(level, message, data = null) {
        if (!DEBUG_CONFIG.enableConsoleLog) return;
        
        const levels = ['error', 'warn', 'info', 'debug'];
        const currentLevelIndex = levels.indexOf(DEBUG_CONFIG.logLevel);
        const messageLevelIndex = levels.indexOf(level);
        
        if (messageLevelIndex <= currentLevelIndex) {
            const timestamp = new Date().toLocaleTimeString();
            const prefix = `[${timestamp}] [${level.toUpperCase()}]`;
            
            if (data) {
                console[level](prefix, message, data);
            } else {
                console[level](prefix, message);
            }
        }
    }
    
    // 检查必需的DOM元素
    function checkRequiredElements() {
        const requiredElements = [
            'net-preset', 'net-custom', 'uploadNetBtn',
            'traffic-preset', 'traffic-custom', 'uploadRouBtn',
            'vehicleType', 'entranceType', 'vipPriority',
            'signal-preset', 'signal-custom', 'uploadAddBtn',
            'roadRestriction', 'signalOptimization',
            'loadResultBtn', 'historyBtn'
        ];
        
        const missingElements = [];
        const foundElements = [];
        
        requiredElements.forEach(id => {
            const element = document.getElementById(id);
            if (element) {
                foundElements.push(id);
            } else {
                missingElements.push(id);
            }
        });
        
        if (missingElements.length > 0) {
            log('error', '缺少必需的DOM元素:', missingElements);
        } else {
            log('info', '所有必需的DOM元素都存在');
        }
        
        return {
            missing: missingElements,
            found: foundElements
        };
    }
    
    // 检查外部依赖
    function checkExternalDependencies() {
        const dependencies = {
            'Chart.js': typeof Chart !== 'undefined',
            'Fetch API': typeof fetch !== 'undefined',
            'LocalStorage': typeof localStorage !== 'undefined',
            'JSON': typeof JSON !== 'undefined'
        };
        
        const missing = [];
        const available = [];
        
        Object.keys(dependencies).forEach(dep => {
            if (dependencies[dep]) {
                available.push(dep);
            } else {
                missing.push(dep);
            }
        });
        
        if (missing.length > 0) {
            log('warn', '缺少外部依赖:', missing);
        } else {
            log('info', '所有外部依赖都可用');
        }
        
        return { missing, available };
    }
    
    // 检查JavaScript错误
    function setupErrorHandling() {
        window.addEventListener('error', function(event) {
            log('error', 'JavaScript运行时错误:', {
                message: event.message,
                filename: event.filename,
                lineno: event.lineno,
                colno: event.colno,
                error: event.error
            });
        });
        
        window.addEventListener('unhandledrejection', function(event) {
            log('error', '未处理的Promise拒绝:', {
                reason: event.reason,
                promise: event.promise
            });
        });
    }
    
    // 检查网络连接
    function checkNetworkConnectivity() {
        if (navigator.onLine) {
            log('info', '网络连接正常');
        } else {
            log('warn', '网络连接异常');
        }
        
        // 测试外部CDN连接
        const testImage = new Image();
        testImage.onload = function() {
            log('info', 'CDN连接正常');
        };
        testImage.onerror = function() {
            log('warn', 'CDN连接可能有问题');
        };
        testImage.src = 'https://cdn.jsdelivr.net/npm/bootstrap-icons@1.10.5/font/bootstrap-icons.css';
    }
    
    // 检查CSS样式加载
    function checkCSSLoading() {
        const stylesheets = document.styleSheets;
        let loadedCount = 0;
        let errorCount = 0;
        
        for (let i = 0; i < stylesheets.length; i++) {
            try {
                const sheet = stylesheets[i];
                if (sheet.cssRules || sheet.rules) {
                    loadedCount++;
                } else {
                    errorCount++;
                }
            } catch (e) {
                errorCount++;
            }
        }
        
        log('info', `CSS样式表状态: ${loadedCount}个已加载, ${errorCount}个有问题`);
    }
    
    // 主要检查函数
    function runDiagnostics() {
        log('info', '开始前端诊断检查...');
        
        const elementCheck = checkRequiredElements();
        const dependencyCheck = checkExternalDependencies();
        
        checkNetworkConnectivity();
        checkCSSLoading();
        
        // 生成诊断报告
        const report = {
            timestamp: new Date().toISOString(),
            elements: elementCheck,
            dependencies: dependencyCheck,
            userAgent: navigator.userAgent,
            url: window.location.href
        };
        
        log('info', '诊断报告:', report);
        
        // 将报告存储到sessionStorage以便查看
        try {
            sessionStorage.setItem('frontend_diagnostic_report', JSON.stringify(report, null, 2));
        } catch (e) {
            log('warn', '无法保存诊断报告到sessionStorage');
        }
        
        return report;
    }
    
    // 初始化调试器
    function initDebugger() {
        log('info', '前端调试器已启动');
        
        // 设置错误处理
        setupErrorHandling();
        
        // 立即运行一次诊断
        runDiagnostics();
        
        // 定期检查
        setInterval(runDiagnostics, DEBUG_CONFIG.checkInterval);
        
        // 暴露调试函数到全局作用域
        window.frontendDebug = {
            runDiagnostics,
            checkRequiredElements,
            checkExternalDependencies,
            log
        };
    }
    
    // 等待DOM加载完成后启动
    if (document.readyState === 'loading') {
        document.addEventListener('DOMContentLoaded', initDebugger);
    } else {
        initDebugger();
    }
    
})();
