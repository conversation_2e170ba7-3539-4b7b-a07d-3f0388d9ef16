<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>地图测试</title>
    <link rel="stylesheet" href="https://unpkg.com/leaflet@1.9.4/dist/leaflet.css" />
    <style>
        body {
            margin: 0;
            padding: 20px;
            background-color: #05202e;
            color: #95D7E3;
            font-family: Arial, sans-serif;
        }
        
        #map-container {
            width: 100%;
            height: 600px;
            border: 1px solid #30abe8;
            border-radius: 8px;
            overflow: hidden;
        }
        
        #test-map {
            width: 100%;
            height: 100%;
            background-color: #05202e;
        }
        
        .leaflet-control-zoom a {
            background-color: rgba(16, 39, 53, 0.9) !important;
            color: #95D7E3 !important;
            border-color: #30abe8 !important;
        }
        
        .leaflet-control-zoom a:hover {
            background-color: rgba(48, 171, 232, 0.3) !important;
            color: #47ebeb !important;
        }
        
        .status {
            margin-bottom: 20px;
            padding: 10px;
            background-color: #102735;
            border-radius: 4px;
        }
    </style>
</head>
<body>
    <h1>地图显示测试</h1>
    <div class="status" id="status">正在初始化地图...</div>
    <div id="map-container">
        <div id="test-map"></div>
    </div>

    <script src="https://unpkg.com/leaflet@1.9.4/dist/leaflet.js"></script>
    <script>
        let map = null;
        let backgroundImageLayer = null;
        
        // 地图配置参数
        const mapConfig = {
            minZoom: -5,
            maxZoom: 6,
            defaultZoom: -1,
            defaultCenter: [3000, 4700]
        };
        
        // 颜色配置
        const colors = {
            road: '#4a4a4a',
            roadBorder: '#333333',
            junction: '#666666'
        };
        
        function updateStatus(message) {
            document.getElementById('status').textContent = message;
            console.log('状态:', message);
        }
        
        // 加载底图
        function loadBackgroundImage() {
            updateStatus('正在加载底图...');
            
            fetch('http://localhost:8888/api/background-image')
                .then(response => response.json())
                .then(data => {
                    if (data.image) {
                        // 移除现有的底图（如果有）
                        if (backgroundImageLayer) {
                            backgroundImageLayer.remove();
                        }
                        
                        // 计算底图边界
                        const centerX = data.centerX || 0;
                        const centerY = data.centerY || 0;
                        const width = data.width || 1000;
                        const height = data.height || 1000;
                        const rotation = data.rotation || 0;
                        
                        // 计算图像边界
                        const halfWidth = width / 2;
                        const halfHeight = height / 2;
                        
                        // 计算图像四个角的坐标
                        const southWest = map.unproject([centerX - halfWidth, -(centerY + halfHeight)], 0);
                        const northEast = map.unproject([centerX + halfWidth, -(centerY - halfHeight)], 0);
                        const bounds = L.latLngBounds(southWest, northEast);
                        
                        // 创建图像覆盖层
                        const imageUrl = `http://localhost:8888/files/${data.image}`;
                        backgroundImageLayer = L.imageOverlay(imageUrl, bounds, {
                            opacity: 0.8,
                            interactive: false
                        }).addTo(map);
                        
                        updateStatus('底图加载完成，正在加载路网...');
                        map.setView(mapConfig.defaultCenter, mapConfig.defaultZoom);
                        
                        // 加载路网
                        loadNetwork();
                    }
                })
                .catch(error => {
                    console.error('加载底图失败:', error);
                    updateStatus('底图加载失败: ' + error.message);
                });
        }
        
        // 加载路网数据
        function loadNetwork() {
            fetch('http://localhost:8888/api/network')
                .then(response => response.json())
                .then(data => {
                    if (data.features && data.features.length > 0) {
                        // 分离不同类型的要素
                        const lanePolygons = data.features.filter(f => f.properties.type === 'lane_polygon');
                        const junctions = data.features.filter(f => f.properties.type === 'junction');
                        
                        // 先添加车道多边形
                        if (lanePolygons.length > 0) {
                            const lanePolygonLayer = L.geoJSON({
                                type: "FeatureCollection",
                                features: lanePolygons
                            }, {
                                coordsToLatLng: function(coords) {
                                    return map.unproject([coords[0], -coords[1]], 0);
                                },
                                style: function(feature) {
                                    return {
                                        color: colors.roadBorder,
                                        weight: 1,
                                        opacity: 1,
                                        fillColor: colors.road,
                                        fillOpacity: 0.8
                                    };
                                }
                            }).addTo(map);
                        }
                        
                        // 然后添加交叉口
                        if (junctions.length > 0) {
                            const junctionLayer = L.geoJSON({
                                type: "FeatureCollection",
                                features: junctions
                            }, {
                                coordsToLatLng: function(coords) {
                                    return map.unproject([coords[0], -coords[1]], 0);
                                },
                                style: function(feature) {
                                    return {
                                        color: "#000000",
                                        weight: 1,
                                        opacity: 1,
                                        fillColor: colors.junction,
                                        fillOpacity: 0.6
                                    };
                                }
                            }).addTo(map);
                        }
                        
                        updateStatus(`地图加载完成！显示了 ${lanePolygons.length} 个车道和 ${junctions.length} 个交叉口`);
                    } else {
                        updateStatus('路网数据为空');
                    }
                })
                .catch(error => {
                    console.error('加载路网失败:', error);
                    updateStatus('路网加载失败: ' + error.message);
                });
        }
        
        // 初始化地图
        function initializeMap() {
            updateStatus('正在初始化Leaflet地图...');
            
            // 初始化Leaflet地图
            map = L.map('test-map', {
                crs: L.CRS.Simple,
                minZoom: mapConfig.minZoom,
                maxZoom: mapConfig.maxZoom,
                zoomControl: true
            });
            
            // 设置初始视图
            map.setView(mapConfig.defaultCenter, mapConfig.defaultZoom);
            
            updateStatus('地图初始化完成，正在加载底图...');
            
            // 开始加载底图
            loadBackgroundImage();
        }
        
        // 页面加载完成后初始化地图
        document.addEventListener('DOMContentLoaded', function() {
            initializeMap();
        });
    </script>
</body>
</html>
